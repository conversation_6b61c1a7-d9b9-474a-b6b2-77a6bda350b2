package com.hengjian.common.tenant.helper;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.spring.SpringMVCUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.baomidou.mybatisplus.core.plugins.IgnoreStrategy;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.hengjian.common.core.constant.CacheNames;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.function.Supplier;

/**
 * 租户助手
 *
 * <AUTHOR> Li
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TenantHelper {

    private static final String DYNAMIC_TENANT_KEY = GlobalConstants.GLOBAL_REDIS_KEY + "dynamicTenant";

    private static final ThreadLocal<String> TEMP_DYNAMIC_TENANT = new TransmittableThreadLocal<>();

    /**
     * 租户功能是否启用
     */
    public static boolean isEnable() {
        return Convert.toBool(SpringUtils.getProperty("tenant.enable"), false);
    }

    /**
     * 开启忽略租户(开启后需手动调用 {@link #disableIgnore()} 关闭)
     */
    public static void enableIgnore() {
        InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
    }

    /**
     * 关闭忽略租户
     */
    public static void disableIgnore() {
        InterceptorIgnoreHelper.clearIgnoreStrategy();
    }

    /**
     * 在忽略租户中执行
     *
     * @param handle 处理执行方法
     */
    public static void ignore(Runnable handle) {
        enableIgnore();
        try {
            handle.run();
        }catch (Exception e){
            log.error("errorMsg",e);
            throw new RuntimeException(e.getMessage());
        } finally {
            disableIgnore();
        }
    }


    /**
     * 在忽略租户中执行
     *
     * @param handle 处理执行方法
     */
    public static void ignore(Runnable handle, TenantType... tenantType) {
        TenantType tenantTypeEnum = LoginHelper.getTenantTypeEnum();
        if (CollUtil.contains(Arrays.asList(tenantType), tenantTypeEnum)) {
            enableIgnore();
            try {
                handle.run();
            }catch (Exception e){
                throw new RuntimeException(e.getMessage());
            } finally {
                disableIgnore();
            }
        } else {
            handle.run();
        }
    }

    /**
     * 在忽略租户中执行
     *
     * @param handle 处理执行方法
     */
    public static <T> T ignore(Supplier<T> handle) {
        enableIgnore();
        try {
            return handle.get();
        }catch (Exception e){
            log.error("errorMsg",e);
            throw new RuntimeException(e.getMessage());
        }
        finally {
            disableIgnore();

        }
    }

    /**
     * 在忽略租户中执行（匹配指定的租户类型才忽略，其余的正常走）
     * @param tenantType
     * @param handle
     * @return
     * @param <T>
     */
    public static <T> T ignore(Supplier<T> handle, TenantType... tenantType) {
        TenantType tenantTypeEnum = LoginHelper.getTenantTypeEnum();
        if (CollUtil.contains(Arrays.asList(tenantType), tenantTypeEnum)) {
            enableIgnore();
            try {
                return handle.get();
            }catch (Exception e){
                throw new RuntimeException(e.getMessage());
            } finally {
                disableIgnore();
            }
        } else {
            return handle.get();
        }
    }

    /**
     * 设置动态租户(一直有效 需要手动清理)
     * <p>
     * 如果为非web环境 那么只在当前线程内生效
     */
    public static void setDynamic(String tenantId) {
        if (!SpringMVCUtil.isWeb()) {
            TEMP_DYNAMIC_TENANT.set(tenantId);
            return;
        }
        String cacheKey = DYNAMIC_TENANT_KEY + ":" + LoginHelper.getUserId();
        RedisUtils.setCacheObject(cacheKey, tenantId);
        SaHolder.getStorage().set(cacheKey, tenantId);
    }

    /**
     * 获取动态租户(一直有效 需要手动清理)
     * <p>
     * 如果为非web环境 那么只在当前线程内生效
     */
    public static String getDynamic() {
        if (!SpringMVCUtil.isWeb()) {
            return TEMP_DYNAMIC_TENANT.get();
        }
        String cacheKey = DYNAMIC_TENANT_KEY + ":" + LoginHelper.getUserId();
        String tenantId = (String) SaHolder.getStorage().get(cacheKey);
        if (StringUtils.isNotBlank(tenantId)) {
            return tenantId;
        }
        tenantId = RedisUtils.getCacheObject(cacheKey);
        SaHolder.getStorage().set(cacheKey, tenantId);
        return tenantId;
    }

    /**
     * 清除动态租户
     */
    public static void clearDynamic() {
        if (!SpringMVCUtil.isWeb()) {
            TEMP_DYNAMIC_TENANT.remove();
            return;
        }
        String cacheKey = DYNAMIC_TENANT_KEY + ":" + LoginHelper.getUserId();
        RedisUtils.deleteObject(cacheKey);
        SaHolder.getStorage().delete(cacheKey);
    }

    /**
     * 获取当前租户id(动态租户优先)
     */
    public static String getTenantId() {
        String tenantId = TenantHelper.getDynamic();
        if (StringUtils.isBlank(tenantId)) {
            tenantId = LoginHelper.getTenantId();
        }
        return tenantId;
    }


    /**
     * 判断是否是全局缓存
     * @param name
     * @return
     */
    public static boolean isGlobal(String name) {
        return StringUtils.containsAny(name, GlobalConstants.GLOBAL_REDIS_KEY, CacheNames.SYS_CONFIG,
            CacheNames.SYS_DICT, CacheNames.SYS_OSS_CONFIG);
    }
}
