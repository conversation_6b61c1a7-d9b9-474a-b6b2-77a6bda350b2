<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zsmall.extend.shop</groupId>
        <artifactId>zsmall-thirdparty-shop</artifactId>
        <version>${zsmall.version}</version>
    </parent>

    <artifactId>zsmall-shop-amazon</artifactId>
    <name>ZS-Mall第三方店铺模块 amazon</name>
    <packaging>pom</packaging>

    <description>
        ZS-Mall第三方店铺模块 amazon
    </description>

    <modules>
        <module>zsmall-shop-amazon-business</module>
        <module>zsmall-shop-amazon-business-controller</module>
        <module>zsmall-shop-amazon-kit</module>
        <module>zsmall-shop-amazon-job</module>
    </modules>

    <dependencies>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <optional>true</optional>
            <scope>test</scope>
        </dependency>

    </dependencies>

</project>
