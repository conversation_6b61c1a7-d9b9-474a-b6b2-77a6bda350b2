package com.zsmall.extend.shop.amazon.business.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 亚马逊应用管理对象 amazon_app_config
 *
 * <AUTHOR> Li
 * @date 2023-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("amazon_app_config")
public class AmazonAppConfig extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private String id;

    /**
     * 应用Id
     */
    private String appId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 应用请求地址
     */
    private String endpoint;

    /**
     * 应用授权信息
     */
    private String authorizeInfo;

    /**
     * 已授权数量
     */
    private Integer authorizedQuantity;

    /**
     * 应用状态
     */
    private Integer appStatus;

    /**
     * 排序号
     */
    private Integer sortNum;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
