package com.zsmall.extend.shop.amazon.business.entity.iservice;

import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.extend.shop.amazon.business.entity.domain.AmazonAppConfig;
import com.zsmall.extend.shop.amazon.business.entity.domain.bo.AmazonAppConfigBo;
import com.zsmall.extend.shop.amazon.business.entity.domain.vo.AmazonAppConfigDetailVo;
import com.zsmall.extend.shop.amazon.business.entity.domain.vo.AmazonAppConfigVo;

import java.util.Collection;
import java.util.List;

/**
 * 亚马逊应用管理Service接口
 *
 * <AUTHOR> Li
 * @date 2023-10-24
 */
public interface IAmazonAppConfigService {

    /**
     * 查询亚马逊应用管理
     */
    AmazonAppConfigDetailVo queryById(String id);

    /**
     * 查询亚马逊应用管理列表
     */
    TableDataInfo<AmazonAppConfigVo> queryPageList(AmazonAppConfigBo bo, PageQuery pageQuery);

    /**
     * 查询亚马逊应用管理列表
     */
    List<AmazonAppConfigVo> queryList(AmazonAppConfigBo bo);

    /**
     * 新增亚马逊应用管理
     */
    Boolean insertByBo(AmazonAppConfigBo bo);

    /**
     * 修改亚马逊应用管理
     */
    Boolean updateByBo(AmazonAppConfigBo bo);

    /**
     * 校验并批量删除亚马逊应用管理信息
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    /**
     * 获取可用的app 主键
     * @return
     */
    String getEnableApp();

    /**
     * 根据主键查询应用配置
     * @param appConfigId
     * @return
     */
    AmazonAppConfig selectConfigById(String appConfigId);

    /**
     * 根据应用Id查询应用配置
     * @param appId
     * @return
     */
    AmazonAppConfig selectConfigByAppId(String appId);
}
