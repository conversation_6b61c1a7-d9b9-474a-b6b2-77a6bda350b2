<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zsmall.extend.shop</groupId>
        <artifactId>zsmall-thirdparty-shop</artifactId>
        <version>${zsmall.version}</version>
    </parent>

    <groupId>com.zsmall.extend.shop</groupId>
    <artifactId>zsmall-shop-tiktok</artifactId>
    <name>ZS-Mall第三方店铺模块 tiktok</name>
    <packaging>pom</packaging>

    <description>
        ZS-Mall第三方店铺模块 tiktok
    </description>

    <modules>
        <module>zsmall-shop-tiktok-business</module>
        <module>zsmall-shop-tiktok-job</module>
        <module>zsmall-shop-tiktok-business-controller</module>
    </modules>

    <dependencies>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <optional>true</optional>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.6.2</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>
