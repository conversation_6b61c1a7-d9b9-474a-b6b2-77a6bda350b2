<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zsmall.extend.payment</groupId>
        <artifactId>zsmall-payment</artifactId>
        <version>${zsmall.version}</version>
    </parent>

    <artifactId>zsmall-payment-payoneer</artifactId>
    <name>ZS-Mall Payoneer模块</name>
    <version>${zsmall.version}</version>
    <description>
        zsmall-Payoneer 支付模块
    </description>

    <properties>

    </properties>

    <dependencies>
        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.javen205</groupId>
            <artifactId>IJPay-Core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.javen205</groupId>
            <artifactId>IJPay-Payoneer</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-json</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-common</artifactId>
        </dependency>
    </dependencies>

</project>
