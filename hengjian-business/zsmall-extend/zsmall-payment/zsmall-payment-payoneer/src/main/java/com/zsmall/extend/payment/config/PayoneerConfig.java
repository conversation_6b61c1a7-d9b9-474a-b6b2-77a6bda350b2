package com.zsmall.extend.payment.config;


import com.ijpay.payoneer.PayoneerApiConfig;
import com.ijpay.payoneer.PayoneerApiConfigKit;
import com.ijpay.payoneer.accesstoken.AccessTokenKit;
import com.zsmall.extend.payment.config.properties.PayoneerProperties;
import com.zsmall.extend.payment.support.IPayoneerAccessTokenCacheImpl;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;

/**
 * Payoneer配置
 */
@AutoConfiguration
@ConditionalOnProperty(value = "payment.payoneer.enabled", havingValue = "true")
//@EnableConfigurationProperties(PayoneerProperties.class)
public class PayoneerConfig {

    @Bean
    public PayoneerApiConfig payoneerApiConfig(PayoneerProperties payoneerProperties) {
        PayoneerApiConfig apiConfig;
        try {
            apiConfig = PayoneerApiConfigKit.getApiConfig();
        } catch (Exception e) {
            PayoneerApiConfig payoneerApiConfig = getApiConfig(payoneerProperties);
            PayoneerApiConfigKit.putApiConfig(payoneerApiConfig);
            apiConfig = payoneerApiConfig;
            AccessTokenKit.setCache(new IPayoneerAccessTokenCacheImpl());
        }
        return apiConfig;
    }

    public PayoneerApiConfig getApiConfig(PayoneerProperties payoneerProperties) {
        PayoneerApiConfig config = new PayoneerApiConfig(payoneerProperties.getClientKey(),
            payoneerProperties.getClientSecret(), payoneerProperties.getProgramId(),
            payoneerProperties.getSandBox(), payoneerProperties.getRedirectUrl());
        return config;
    }

}
