package com.zsmall.extend.payment.bean.pay.PayoneerListsResponse;

import com.fasterxml.jackson.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.OffsetDateTime;
import java.util.List;

@Data
public class AccountElement implements Serializable {

    private static final long serialVersionUID = 1L;

    private AccountLinks links;
    private String id;
    private String code;
    private String label;
    private String method;
    private String button;
    private String operationType;
    private MaskedAccount maskedAccount;
    private Boolean registration;
    private Boolean recurrence;
    private OffsetDateTime lastSuccessfulChargeAt;
    private Boolean selected;
    private Long iFrameHeight;
    private OffsetDateTime preferredAt;
    private String createdAt;
    private Boolean emptyForm;
    private List<LocalizedInputElement> localizedInputElements;
    private List<InputElement> inputElements;
    private Routing routing;
    private String state;
    private String stateReason;
    private String stateModificationDate;
    private ContractData contractData;
    private RiskProvider providerResponse;
    private String deferral;
}
