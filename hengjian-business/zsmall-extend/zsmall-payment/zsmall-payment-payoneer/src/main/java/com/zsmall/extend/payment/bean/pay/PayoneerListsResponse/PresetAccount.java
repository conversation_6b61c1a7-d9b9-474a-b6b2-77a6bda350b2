package com.zsmall.extend.payment.bean.pay.PayoneerListsResponse;

import com.fasterxml.jackson.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class PresetAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    private PresetAccountLinks links;
    private String code;
    private MaskedAccount maskedAccount;
    private Boolean emptyForm;
    private String button;
    private Redirect redirect;
    private String operationType;
    private String method;
    private String label;
    private String deferral;
    private ContractData contractData;
    private Boolean registered;
    private List<String> providers;
}
