package com.zsmall.extend.payment.bean.pay.PayoneerListsResponse;

import com.fasterxml.jackson.annotation.*;
import lombok.Data;

import java.io.Serializable;

@Data
public class VerificationCodeOptions implements Serializable {

    private static final long serialVersionUID = 1L;

    private Boolean requiredForRegistration;
    private Boolean requiredForOneClickPayment;
    private Boolean requiredForFirstPayment;
}
