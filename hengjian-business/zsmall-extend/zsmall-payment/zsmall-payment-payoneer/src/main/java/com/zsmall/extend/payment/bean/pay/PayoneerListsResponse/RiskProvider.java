package com.zsmall.extend.payment.bean.pay.PayoneerListsResponse;

import com.fasterxml.jackson.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RiskProvider implements Serializable {

    private static final long serialVersionUID = 1L;

    private String providerCode;
    private List<Header> parameters;
    private String providerType;
    private RiskProviderLinks links;
}
