package com.zsmall.extend.payment.bean.pay.PayoneerListsResponse;

import com.fasterxml.jackson.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PayoneerListsResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    private PayoneerListsResponseLinks links;
    private String timestamp;
    private String operation;
    private Payment payment;
    private List<Product> products;
    private List<Shipping> shipping;
    private String resultCode;
    private String resultInfo;
    private ReturnCode returnCode;
    private Status status;
    private Interaction interaction;
    private Identification identification;
    private PspReference pspReference;
    private List<AccountElement> accounts;
    private Networks networks;
    private ExtraElements extraElements;
    private Redirect redirect;
    private PresetAccount presetAccount;
    private String operationType;
    private String integrationType;
    private Boolean allowDelete;
    private Style style;
    private ClientInfo clientInfo;
    private List<RiskProvider> riskProviders;
    private ProcessingModel processingModel;
}
