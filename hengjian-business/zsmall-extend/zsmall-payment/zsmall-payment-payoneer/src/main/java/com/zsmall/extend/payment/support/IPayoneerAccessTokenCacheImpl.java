package com.zsmall.extend.payment.support;

import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.redis.utils.RedisUtils;
import com.ijpay.payoneer.cache.IAccessTokenCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * Payoneer accesstoken 缓存实现
 */
@Slf4j
@Service
@ConditionalOnProperty(value = "payment.payoneer.enabled", havingValue = "true")
public class IPayoneerAccessTokenCacheImpl implements IAccessTokenCache {

    @Override
    public String get(String key) {
        String redisKey = getRedisKey(key);
        return RedisUtils.getCacheObject(redisKey);
    }

    @Override
    public void set(String key, String jsonValue) {
        String redisKey = getRedisKey(key);
        RedisUtils.setCacheObject(redisKey, jsonValue);
    }

    @Override
    public void remove(String key) {
        String redisKey = getRedisKey(key);
        RedisUtils.deleteObject(redisKey);
    }

    private String getRedisKey(String key) {
        return GlobalConstants.GLOBAL_REDIS_KEY + key;
    }
}
