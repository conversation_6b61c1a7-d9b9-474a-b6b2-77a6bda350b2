package com.zsmall.extend.payment.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.hengjian.common.core.constant.Constants;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.extend.utils.SystemEventUtils;
import com.hengjian.system.domain.vo.SysOssVo;
import com.zsmall.common.properties.FileProperties;
import com.zsmall.common.util.OkHttpUtil;
import lombok.extern.log4j.Log4j;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.zip.GZIPInputStream;

import static cn.hutool.poi.excel.sax.AttributeName.s;

/**
 * <AUTHOR>
 * @date 2024年4月2日  11:19
 * @description:
 */
@Slf4j
public class HttpUtil {

    /**
     * 功能描述：
     *
     * @param header
     * @param url
     * @param bodyJson
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/02/27
     */
    public static String postApi(HashMap<String, String> header, String url, String bodyJson) {
        OkHttpClient client = new OkHttpClient();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body;
        if(StringUtils.isEmpty(bodyJson)){
            body = RequestBody.create(mediaType, "");
        }else {
            body = RequestBody.create(mediaType, bodyJson);
        }

        StringBuilder result = new StringBuilder();
        BufferedReader in = null;
        try {
            Request.Builder builder = new Request.Builder().url(url).addHeader("Content-Type", "application/json");
            if (header != null) {
                Set<Map.Entry<String, String>> entries = header.entrySet();
                for (Map.Entry<String, String> entry : entries) {
                    builder.addHeader(entry.getKey(), entry.getValue());
                }

            }
            Request request = builder.post(body).build();

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.error("参数:{}, 调用接口失败response code: {} and message: {}", s,
                    response.code(), response.message());
            }
            ResponseBody responseBody = response.body();
            InputStream inputStream = responseBody.byteStream();
            in = new BufferedReader(new InputStreamReader(inputStream));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            in.close();
            log.info("response body result:{}", result);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result.toString();
    }


    /**
     * 向指定 URL 发送GET方法的请求
     *
     * @param url   发送请求的 URL
     * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return 所代表远程资源的响应结果
     */
    public static String sendGet(String url, String param) {
        return sendGet(url, param, Constants.UTF8, null);
    }

    /**
     * 向指定 URL 发送GET方法的请求
     *
     * @param url   发送请求的 URL
     * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @param param 请求头参数
     * @return 所代表远程资源的响应结果
     */
    public static String sendGet(String url, String param, Map<String, String> headerMap) {
        return sendGet(url, param, Constants.UTF8, headerMap);
    }

    /**
     * 向指定 URL 发送GET方法的请求
     *
     * @param url          发送请求的 URL
     * @param param        请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @param encodingType 编码类型
     * @param param        请求头参数
     * @return 所代表远程资源的响应结果
     */
    public static String sendGet(String url, String param, String encodingType, Map<String, String> headerMap) {
        StringBuilder result = new StringBuilder();
        BufferedReader in = null;
        try {
            String urlNameString = url;
            if (StringUtils.isNotEmpty(param)) {
                urlNameString = url + "?" + param;
            }
            log.info("sendGet - {}", urlNameString);
            URL realUrl = new URL(urlNameString);
            URLConnection connection = realUrl.openConnection();
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent", "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36");

            if (null != headerMap && !headerMap.isEmpty()) {
                Set<String> keySet = headerMap.keySet();
                for (String keyStr : keySet) {
                    connection.setRequestProperty(keyStr, headerMap.get(keyStr));
                }
            }

            connection.connect();
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), encodingType));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            log.info("recv - {}", result);
        } catch (ConnectException e) {
            log.error("调用HttpUtils.sendGet ConnectException, url=" + url + ",param=" + param, e);
        } catch (SocketTimeoutException e) {
            log.error("调用HttpUtils.sendGet SocketTimeoutException, url=" + url + ",param=" + param, e);
        } catch (IOException e) {
            log.error("调用HttpUtils.sendGet IOException, url=" + url + ",param=" + param, e);
        } catch (Exception e) {
            log.error("调用HttpsUtil.sendGet Exception, url=" + url + ",param=" + param, e);
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception ex) {
                log.error("调用in.close Exception, url=" + url + ",param=" + param, ex);
            }
        }
        return result.toString();
    }


    /**
     * 发送GET请求到指定的URL并处理gzip压缩的响应体。
     *
     * @param urlString 请求的URL地址
     * @return 响应内容的字符串
     * @throws Exception 可能发生的任何异常
     */
    public static String sendGetRequest(String urlString, Map<String, String> headerMap) throws Exception {
        InputStream inputStream = null;
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent", "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36");
            if (null != headerMap && !headerMap.isEmpty()) {
                Set<String> keySet = headerMap.keySet();
                for (String keyStr : keySet) {
                    connection.setRequestProperty(keyStr, headerMap.get(keyStr));
                }
            }

            // 发送请求并获得响应码
            int responseCode = connection.getResponseCode();
            System.out.println("Response Code: " + responseCode);

            // 检查响应头以确定内容是否被gzip压缩
            String contentEncoding = connection.getHeaderField("Content-Encoding");
            inputStream = (contentEncoding != null && contentEncoding.equalsIgnoreCase("gzip"))
                ? new GZIPInputStream(connection.getInputStream())
                : connection.getInputStream();

            // 读取响应体并返回
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            return response.toString();
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }
}
