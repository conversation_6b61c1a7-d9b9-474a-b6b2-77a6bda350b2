package com.zsmall.extend.payment.config.properties;

import lombok.Data;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Payoneer配置
 */
@Data
@AutoConfiguration
@ConfigurationProperties(prefix = "payment.payoneer")
public class PayoneerProperties {
    /**
     * 过滤开关
     */
    private Boolean enabled;
    /**
     * Client Key客户秘钥
     */
    private String clientKey;
    /**
     * Client Secret机密秘钥
     */
    private String clientSecret;
    /**
     * 商户Id
     */
    private String programId;
    /**
     * 是否是沙箱环境
     */
    private Boolean sandBox;
    /**
     * 应用重定向地址
     */
    private String redirectUrl;

    /**
     * 发起授权有效时间（单位：秒）
     */
    private Long authorizeEffectiveTime = 300L;
    /**
     * payoneer绑定操作结束后跳转的地址
     */
    private String lastRedirectUrl;

    /**
     * 用于判断校验token超期天数标准
     */
    private Integer additionalTokenExpiredDay = 1;

    /**
     * payoneer支付数据过期偏移量（分钟）
     */
    private Integer paymentExpireOffset;
}
