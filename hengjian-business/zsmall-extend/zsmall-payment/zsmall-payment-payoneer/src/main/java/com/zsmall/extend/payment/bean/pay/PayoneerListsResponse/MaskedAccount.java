package com.zsmall.extend.payment.bean.pay.PayoneerListsResponse;

import com.fasterxml.jackson.annotation.*;
import lombok.Data;

import java.io.Serializable;

@Data
public class MaskedAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    private Registration registration;
    private String displayLabel;
    private String holderName;
    private String number;
    private String bankCode;
    private String bankName;
    private String bic;
    private String branch;
    private String city;
    private Long expiryMonth;
    private Long expiryYear;
    private String iban;
    private String login;
}
