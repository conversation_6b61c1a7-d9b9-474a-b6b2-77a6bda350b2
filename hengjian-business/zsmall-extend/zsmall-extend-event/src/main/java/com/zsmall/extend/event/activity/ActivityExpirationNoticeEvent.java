package com.zsmall.extend.event.activity;

import cn.hutool.json.JSONArray;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 活动到期提醒事件
 *
 * <AUTHOR>
 * @date 2023/8/4
 */
@Data
@AllArgsConstructor
public class ActivityExpirationNoticeEvent {

    /**
     * 登录令牌
     */
    private String token;

    /**
     * 分销商租户
     */
    private String dTenantId;

    /**
     * 消息数组
     */
    private JSONArray messageArray;

    public ActivityExpirationNoticeEvent(String token, String dTenantId) {
        this.token = token;
        this.dTenantId = dTenantId;
    }
}
