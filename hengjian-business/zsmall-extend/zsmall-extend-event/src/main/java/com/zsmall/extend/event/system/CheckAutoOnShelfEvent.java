package com.zsmall.extend.event.system;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 校验自动上架设置事件
 *
 * <AUTHOR>
 * @date 2023/6/30
 */
@Data
@AllArgsConstructor
public class CheckAutoOnShelfEvent {

    /**
     * 入参：租户ID
     */
    private String tenantId;

    /**
     * 是否开启自动支付
     */
    private Boolean autoOnShelf;

    public CheckAutoOnShelfEvent(String tenantId) {
        this.tenantId = tenantId;
    }
}
