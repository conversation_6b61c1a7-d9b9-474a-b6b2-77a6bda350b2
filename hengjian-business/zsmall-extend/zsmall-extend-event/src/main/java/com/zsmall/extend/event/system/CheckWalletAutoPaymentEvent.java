package com.zsmall.extend.event.system;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025年1月24日  14:19
 * @description: 校验租户钱包是否开启自动支付事件
 */
@Data
@AllArgsConstructor
public class CheckWalletAutoPaymentEvent {

    /**
     * 入参：租户ID
     */
    private String tenantId;

    /**
     * 币种
     */
    private String currency;

    /**
     * 是否开启自动支付
     */
    private Boolean autoPay;

    public CheckWalletAutoPaymentEvent(String tenantId) {
        this.tenantId = tenantId;
    }

    public CheckWalletAutoPaymentEvent(String tenantId, String currency) {
        this.tenantId = tenantId;
        this.currency = currency;
    }
}
