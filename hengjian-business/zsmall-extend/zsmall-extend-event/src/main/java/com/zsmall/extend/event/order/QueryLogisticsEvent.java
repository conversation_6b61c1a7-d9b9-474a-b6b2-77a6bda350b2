package com.zsmall.extend.event.order;

import com.zsmall.order.entity.domain.OrderItemTrackingRecord;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 查询物流事件
 *
 * <AUTHOR>
 * @date 2023/7/13
 */
@Data
@Getter
@Setter
@AllArgsConstructor
public class QueryLogisticsEvent {

    /**
     * 入参：跟踪单记录
     */
    private OrderItemTrackingRecord trackingRecord;

}
