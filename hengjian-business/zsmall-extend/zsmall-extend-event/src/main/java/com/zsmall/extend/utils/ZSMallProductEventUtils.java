package com.zsmall.extend.utils;

import com.hengjian.common.core.utils.SpringUtils;
import com.zsmall.extend.event.product.EsProductUploadEvent;
import com.zsmall.extend.event.product.SetProductSkuStockEvent;
import com.zsmall.extend.event.product.TaskStockSyncEvent;
import com.zsmall.product.entity.domain.ProductSku;

/**
 * ZSMall-商品相关事件工具
 *
 * <AUTHOR>
 * @date 2023/7/24
 */
public class ZSMallProductEventUtils {

    /**
     * 设置商品SKU库存
     * @param productSku
     */
    public static void setProductSkuStock(ProductSku productSku) {
        SetProductSkuStockEvent event = new SetProductSkuStockEvent(productSku);
        SpringUtils.context().publishEvent(event);
    }

    /**
     * 创建库存同步任务
     * @param productSkuId
     */
    public static void createTaskStockSync(Long productSkuId) {
        TaskStockSyncEvent event = new TaskStockSyncEvent(productSkuId);
        SpringUtils.context().publishEvent(event);
    }

    /**
     * 创建库存同步任务
     * @param productSkuCode
     */
    public static void createTaskStockSync(String productSkuCode) {
        TaskStockSyncEvent event = new TaskStockSyncEvent(productSkuCode);
        SpringUtils.context().publishEvent(event);
    }

    /**
     * ES数据上传
     */
    public static void esProductSkuUpload(String... productSkuCode) {
        EsProductUploadEvent event = EsProductUploadEvent.byProductSkuCode(productSkuCode);
        SpringUtils.context().publishEvent(event);
    }

}
