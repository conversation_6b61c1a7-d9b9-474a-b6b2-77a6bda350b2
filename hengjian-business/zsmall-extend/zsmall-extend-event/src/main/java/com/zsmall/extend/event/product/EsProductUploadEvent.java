package com.zsmall.extend.event.product;

import lombok.Data;

/**
 * Es商品数据上传事件
 *
 * <AUTHOR>
 * @date 2023/8/11
 */
@Data
public class EsProductUploadEvent {

    private Type type;

    private String productCode;

    private String[] productSkuCodes;

    /**
     * 私有化构造
     */
    private EsProductUploadEvent() {}

    public static EsProductUploadEvent byProductCode(String productCode) {
        EsProductUploadEvent esProductUploadEvent = new EsProductUploadEvent();
        esProductUploadEvent.setType(Type.ProductCode);
        esProductUploadEvent.setProductCode(productCode);
        return esProductUploadEvent;
    }

    public static EsProductUploadEvent byProductSkuCode(String... productSkuCodes) {
        EsProductUploadEvent esProductUploadEvent = new EsProductUploadEvent();
        esProductUploadEvent.setType(Type.ProductSkuCode);
        esProductUploadEvent.setProductSkuCodes(productSkuCodes);
        return esProductUploadEvent;
    }

    public enum Type {

        ProductCode,
        ProductSkuCode,
        ;

    }

}
