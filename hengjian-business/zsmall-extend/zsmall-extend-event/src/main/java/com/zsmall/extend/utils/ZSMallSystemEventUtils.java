package com.zsmall.extend.utils;

import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.zsmall.extend.event.system.*;

import java.lang.reflect.UndeclaredThrowableException;
import java.util.List;

/**
 * 系统模块事件工具类
 */
public class ZSMallSystemEventUtils {

    /**
     * 事件-分销商用户注册成功后，创建默认支付信息
     *
     * @param tenantId
     */
    public static void initDistrPaymentInfo(String tenantId) {
        DistrPaymentInfoInitEvent paymentInfoInitEvent = new DistrPaymentInfoInitEvent(tenantId);
        SpringUtils.context().publishEvent(paymentInfoInitEvent);
    }

    /**
     * 事件-用户注册成功后，创建默认钱包
     *
     * @param tenantId
     */
    public static void initWalletInfo(String tenantId) {
        WalletInitEvent walletInitEvent = new WalletInitEvent(tenantId);
        SpringUtils.context().publishEvent(walletInitEvent);
    }

    /**
     * 事件-用户注册成功后，初始化账单
     *
     * @param tenantId
     */
    public static void initBill(String tenantId) {
        BillInitEvent billInitEvent = new BillInitEvent(tenantId);
        SpringUtils.context().publishEvent(billInitEvent);
    }

    /**
     * 事件-生成账单（提前生成下一期）
     * @param tenantList
     */
    public static void generateBill(List<SysTenantVo> tenantList) {
        BillGenerateEvent event = new BillGenerateEvent(tenantList);
        SpringUtils.context().publishEvent(event);
    }

    /**
     * 事件-校验是否开启自动支付
     * @param tenantId
     * @return
     */
    public static Boolean checkAutoPaymentEvent(String tenantId) {
        CheckAutoPaymentEvent event = new CheckAutoPaymentEvent(tenantId);
        SpringUtils.context().publishEvent(event);
        return event.getAutoPay();
    }

    /**
     * 事件-校验钱包是否开启自动支付
     * @param tenantId
     * @param currency
     * @return
     */
    public static Boolean checkWalletAutoPaymentEvent(String tenantId,String currency) {
        CheckWalletAutoPaymentEvent event = new CheckWalletAutoPaymentEvent(tenantId,currency);
        SpringUtils.context().publishEvent(event);
        return event.getAutoPay();
    }

    /**
     * 事件-校验自动上架设置
     * @param tenantId
     * @return
     */
    public static Boolean checkAutoOnShelfEvent(String tenantId) {
        CheckAutoOnShelfEvent event = new CheckAutoOnShelfEvent(tenantId);
        SpringUtils.context().publishEvent(event);
        return event.getAutoOnShelf();
    }

    /**
     * 事件-初始化额外设置
     * @param tenantId
     * @return
     */
    public static void initExtraSettingsEvent(String tenantId, TenantType tenantType) {
        InitExtraSettingEvent event = new InitExtraSettingEvent(tenantId, tenantType);
        SpringUtils.context().publishEvent(event);
    }

    /**
     * 事件-检查分销商信息是否完善
     * @throws RStatusCodeException
     */
    public static void checkDisInfoPerfection(String tenantId) {
        try {
            SpringUtils.context().publishEvent(new CheckDisInfoPerfectionEvent(tenantId));
        } catch (UndeclaredThrowableException e) {
            Throwable undeclaredThrowable = e.getUndeclaredThrowable();
            if (undeclaredThrowable instanceof RStatusCodeException) {
                throw (RStatusCodeException) undeclaredThrowable;
            }
        }
    }

}
