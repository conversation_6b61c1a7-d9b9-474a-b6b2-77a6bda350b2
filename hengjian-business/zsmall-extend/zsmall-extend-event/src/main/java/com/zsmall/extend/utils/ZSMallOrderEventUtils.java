package com.zsmall.extend.utils;

import com.hengjian.common.core.utils.SpringUtils;
import com.zsmall.extend.event.order.QueryLogisticsEvent;
import com.zsmall.order.entity.domain.OrderItemTrackingRecord;
import com.zsmall.order.entity.domain.Orders;

import java.util.List;
import java.util.Set;

/**
 * ZSMall-订单相关事件工具
 *
 * <AUTHOR>
 * @date 2023/7/13
 */
public class ZSMallOrderEventUtils {

    /**
     * 事件-查询物流信息
     */
    public static void queryLogistics(OrderItemTrackingRecord trackingRecord) {
        QueryLogisticsEvent event = new QueryLogisticsEvent(trackingRecord);
        SpringUtils.context().publishEvent(event);
    }

}
