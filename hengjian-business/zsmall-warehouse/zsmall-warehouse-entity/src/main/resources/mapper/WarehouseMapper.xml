<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.warehouse.entity.mapper.WarehouseMapper">

    <select id="existsWarehouseSystemCode" resultType="java.lang.Boolean">
        SELECT COUNT(wrs.id) FROM warehouse wrs WHERE wrs.warehouse_system_code = #{warehouseSystemCode}
    </select>

    <select id="isSupportThirdCarrier" resultType="java.lang.Boolean">
        SELECT COUNT(sw.id)
        FROM warehouse sw
        WHERE sw.del_flag = '0'
          AND sw.warehouse_system_code = #{warehouseSystemCode}
          AND sw.support_logistics_account = 1
    </select>

    <select id="getWarehouseByProductSkuCode" resultType="com.zsmall.warehouse.entity.domain.Warehouse">
        SELECT wa.id,
               wa.tenant_id,
               wa.warehouse_type,
               wa.warehouse_name,
               wa.warehouse_code,
               wa.warehouse_system_code,
               wa.warehouse_state,
               wa.zip_code,
               wa.country,
               wa.support_logistics_account,
               wa.del_flag,
               wa.create_by,
               wa.create_time,
               wa.update_by,
               wa.update_time
        FROM warehouse wa
                 INNER JOIN product_sku_stock pss
                            ON wa.warehouse_system_code = pss.warehouse_system_code
        WHERE wa.del_flag = 0
        and pss.del_flag='0'
          AND wa.warehouse_state = 1
          AND pss.product_sku_code = #{productSkuCode,jdbcType=VARCHAR}
    </select>

    <select id="getWarehouseAddressInfo" resultType="java.lang.String">
        SELECT CONCAT(w.warehouse_name, ' (', CONCAT_WS(',', wa.state, wa.city, wa.address1, wa.address2), ')') AS warehouseInfo
        FROM (
        SELECT *
        FROM warehouse
        WHERE del_flag = '0'
        AND warehouse_state = 1
        <if test="warehouseSystemCode != null and warehouseSystemCode != ''">
            and warehouse_system_code=#{warehouseSystemCode}
        </if>
        ) w
        LEFT JOIN warehouse_address wa ON w.id = wa.warehouse_id AND wa.del_flag = 0;
    </select>

    <select id="listCountryByWarehouseSystemCode" resultType="java.lang.String">
        select distinct t2.country_code
        from warehouse t1
                 left join warehouse_delivery_country t2 on t1.id = t2.warehouse_id and t2.del_flag = 0
        where t1.warehouse_system_code = #{warehouseSystemCode}
          and t1.del_flag = 0

    </select>
</mapper>
