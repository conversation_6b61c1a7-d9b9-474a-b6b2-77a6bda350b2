<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.warehouse.entity.mapper.WarehouseDeliveryCountryMapper">
    <select id="getWarehouseDeliveryCountryByWarehouseCodes"
            resultType="com.zsmall.warehouse.entity.domain.WarehouseDeliveryCountry">
        select wdc.*
        from warehouse w
        inner  join warehouse_delivery_country wdc on w.id = wdc.warehouse_id
        where w.del_flag=0
        and  wdc.del_flag=0
        and  w.warehouse_code=#{warehouseCode}
    </select>

    <delete id="removeByWarehouseId">
        delete
        from warehouse_delivery_country
        where warehouse_id =#{warehouseId};
    </delete>
</mapper>
