<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.warehouse.entity.mapper.LogisticsTemplateRateRuleMapper">

    <select id="queryByProductSkuCode" resultType="com.zsmall.warehouse.entity.domain.LogisticsTemplateRateRule">
        SELECT ltrr.*
        FROM logistics_template_rate_rule ltrr
                 JOIN logistics_rate_country_relation lrcr on ltrr.id = lrcr.logistics_template_rate_rule_id
                 JOIN logistics_template lt on lrcr.logistics_template_id = lt.id
        WHERE lt.del_flag = '0'
          AND lrcr.del_flag = '0'
          AND ltrr.del_flag = '0'
          AND lt.logistics_template_no IN
              (SELECT pss.logistics_template_no
               FROM product_sku_stock pss
               WHERE pss.del_flag = '0'
                 AND pss.stock_state = 1
                 AND pss.product_sku_code = #{productSkuCode}
                 AND pss.logistics_template_no IS NOT NULL)
        GROUP BY ltrr.id
    </select>
</mapper>
