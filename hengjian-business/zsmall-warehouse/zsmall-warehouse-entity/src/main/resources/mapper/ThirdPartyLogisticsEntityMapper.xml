<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.warehouse.entity.mapper.ThirdPartyLogisticsEntityMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.warehouse.entity.domain.ThirdPartyLogisticsEntity">
            <id property="slug" column="slug" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="defaultLanguage" column="default_language" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="otherName" column="other_name" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="statusType" column="status_type" jdbcType="VARCHAR"/>
            <result property="webUrl" column="web_url" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        slug,create_time,update_time,
        default_language,name,other_name,
        phone,status_type,web_url
    </sql>

</mapper>
