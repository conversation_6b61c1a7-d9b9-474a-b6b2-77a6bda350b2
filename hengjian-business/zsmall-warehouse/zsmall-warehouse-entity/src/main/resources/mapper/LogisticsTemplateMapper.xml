<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.warehouse.entity.mapper.LogisticsTemplateMapper">


    <select id="existsLogisticsTemplateCode" resultType="java.lang.Boolean">
        SELECT COUNT(lt.id) FROM logistics_template lt WHERE lt.logistics_template_no = #{logisticsTemplateCode}
    </select>

    <select id="queryByWarehouse" resultType="com.zsmall.warehouse.entity.domain.LogisticsTemplate">
        SELECT pt.*
        FROM logistics_template pt
                 JOIN logistics_warehouse_relation lwr on pt.id = lwr.logistics_template_id
                 JOIN warehouse w on lwr.warehouse_id = w.id
        WHERE pt.del_flag = '0' AND lwr.del_flag = '0' AND w.del_flag = '0' AND w.warehouse_system_code = #{warehouseSystemCode}
    </select>
    <select id="queryByWarehouses" resultType="java.util.Map">
        SELECT pt.*, w.warehouse_system_code as warehouseSystemCode
        FROM logistics_template pt
        JOIN logistics_warehouse_relation lwr on pt.id = lwr.logistics_template_id
        JOIN warehouse w on lwr.warehouse_id = w.id
        WHERE pt.del_flag = '0'
        AND lwr.del_flag = '0'
        AND w.del_flag = '0'
        <if test="warehouseSystemCode != null and warehouseSystemCode.size() != 0">
            AND w.warehouse_system_code in
            <foreach collection="warehouseSystemCode" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>

    <select id="queryAdequateStockLogisticsTemplate" resultType="com.zsmall.warehouse.entity.domain.LogisticsTemplate">
        SELECT lt.*
        FROM logistics_template lt JOIN product_sku_stock pss ON lt.logistics_template_no = pss.logistics_template_no
        WHERE pss.product_sku_code = #{productSkuCode}
        AND pss.stock_available >= ABS(#{adjustQuantity})
        AND EXISTS(
            SELECT 1 FROM warehouse w WHERE pss.warehouse_system_code = w.warehouse_system_code
            AND w.del_flag = '0'
            AND w.country = 'US'
            <if test="logisticsAccount == true">
                AND w.support_logistics_account = #{logisticsAccount}
            </if>
        )
        AND lt.del_flag = '0'
        AND pss.stock_state = '1'
        AND pss.del_flag = '0'
    </select>

    <select id="queryByWarehouseAndTemplateName"
            resultType="com.zsmall.warehouse.entity.domain.LogisticsTemplate">
        SELECT pt.*
        FROM logistics_template pt
                 JOIN logistics_warehouse_relation lwr on pt.id = lwr.logistics_template_id
                 JOIN warehouse w on lwr.warehouse_id = w.id
        WHERE pt.del_flag = '0'
          AND lwr.del_flag = '0'
          AND w.del_flag = '0'
          AND pt.template_name = #{templateName}
          AND w.warehouse_system_code = #{warehouseSystemCode}
    </select>

</mapper>
