<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.warehouse.entity.mapper.LogisticsConditionMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.warehouse.entity.domain.LogisticsConditionEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="country" column="country" jdbcType="VARCHAR"/>
            <result property="fulfillerName" column="fulfiller_name" jdbcType="VARCHAR"/>
            <result property="lengthUnit" column="length_unit" jdbcType="VARCHAR"/>
            <result property="logisticsCompany" column="logistics_company" jdbcType="VARCHAR"/>
            <result property="logisticsCompanyId" column="logistics_company_id" jdbcType="VARCHAR"/>
            <result property="logisticsId" column="logistics_id" jdbcType="BIGINT"/>
            <result property="statusType" column="status_type" jdbcType="VARCHAR"/>
            <result property="weightUnit" column="weight_unit" jdbcType="VARCHAR"/>
            <result property="storeWarehouseId" column="store_warehouse_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        country,fulfiller_name,length_unit,
        logistics_company,logistics_company_id,logistics_id,
        status_type,weight_unit,store_warehouse_id
    </sql>
</mapper>
