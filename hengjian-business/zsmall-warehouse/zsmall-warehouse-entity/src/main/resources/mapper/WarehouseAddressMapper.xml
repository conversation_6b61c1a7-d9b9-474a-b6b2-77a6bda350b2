<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.warehouse.entity.mapper.WarehouseAddressMapper">

    <select id="queryByProductSkuCode" resultType="com.zsmall.warehouse.entity.domain.WarehouseAddress">
        SELECT wa.*
        FROM warehouse_address wa
                 JOIN warehouse w on wa.warehouse_id = w.id
        WHERE w.del_flag = '0'
          AND EXISTS(SELECT 1
                     FROM product_sku ps
                              JOIN product_sku_stock pss on ps.product_sku_code = pss.product_sku_code
                     WHERE ps.del_flag = '0'
                       AND pss.del_flag = '0'
                       AND ps.product_sku_code = #{productSkuCode}
                       AND pss.warehouse_system_code = w.warehouse_system_code)

    </select>

    <select id="queryByProductMappingChannel" resultType="com.zsmall.warehouse.entity.domain.WarehouseAddress">
        SELECT wa.*
        FROM warehouse_address wa
                 JOIN warehouse w on wa.warehouse_id = w.id
        WHERE w.del_flag = '0'
          AND EXISTS(SELECT 1
                     FROM product_sku ps
                              JOIN product_sku_stock pss on ps.product_sku_code = pss.product_sku_code
                     WHERE ps.del_flag = '0'
                       AND pss.del_flag = '0'
                       AND EXISTS(SELECT 1 FROM product_mapping pm WHERE pm.product_sku_code = ps.product_sku_code
                                                                     AND pm.del_flag = '0' AND pm.channel_id = #{channelId})
                       <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryValue)">
                           AND ps.product_sku_code = #{queryValue}
                       </if>
                       AND pss.warehouse_system_code = w.warehouse_system_code)
    </select>
</mapper>
