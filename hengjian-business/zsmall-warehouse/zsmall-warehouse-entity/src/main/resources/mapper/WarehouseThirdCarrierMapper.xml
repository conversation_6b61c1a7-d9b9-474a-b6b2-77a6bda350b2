<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.warehouse.entity.mapper.WarehouseThirdCarrierMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.warehouse.entity.domain.WarehouseThirdCarrierEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="carrierName" column="carrier_name" jdbcType="VARCHAR"/>
            <result property="carrierType" column="carrier_type" jdbcType="VARCHAR"/>
            <result property="warehouseId" column="warehouse_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,business_name,business_type,
        warehouse_id,create_time,update_time
    </sql>
</mapper>
