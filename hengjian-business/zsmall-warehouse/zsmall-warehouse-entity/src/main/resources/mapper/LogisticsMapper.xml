<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.warehouse.entity.mapper.LogisticsMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.warehouse.entity.domain.LogisticsEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="logisticsName" column="logistics_name" jdbcType="VARCHAR"/>
            <result property="countryId" column="country_id" jdbcType="BIGINT"/>
            <result property="statusType" column="status_type" jdbcType="VARCHAR"/>
            <result property="storeId" column="store_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        logistics_name,country_id,status_type,
        store_id
    </sql>

    <select id="getLogisticsEntityList" resultMap="BaseResultMap">

        select l from logistics l
            join store s ON s.id = l.store_id
        where l.status_type = 'Enable'
        and s.store_type = 'Md'

    </select>
</mapper>
