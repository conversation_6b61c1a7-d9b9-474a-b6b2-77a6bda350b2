<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.warehouse.entity.mapper.LogisticsConditionInfoMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.warehouse.entity.domain.LogisticsConditionInfoEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="height" column="height" jdbcType="DOUBLE"/>
            <result property="length" column="length" jdbcType="DOUBLE"/>
            <result property="logisticsConditionId" column="logistics_condition_id" jdbcType="BIGINT"/>
            <result property="maxWeight" column="max_weight" jdbcType="DOUBLE"/>
            <result property="maxZone" column="max_zone" jdbcType="BIGINT"/>
            <result property="minWeight" column="min_weight" jdbcType="DOUBLE"/>
            <result property="minZone" column="min_zone" jdbcType="BIGINT"/>
            <result property="shippingCost" column="shipping_cost" jdbcType="DOUBLE"/>
            <result property="statusType" column="status_type" jdbcType="VARCHAR"/>
            <result property="width" column="width" jdbcType="DOUBLE"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        height,length,logistics_condition_id,
        max_weight,max_zone,min_weight,
        min_zone,shipping_cost,status_type,
        width
    </sql>
</mapper>
