package com.zsmall.warehouse.entity.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 第三方物流商信息
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "第三方物流商信息")
public class RespLogisticsInfoBody {

  @Schema(title = "物流商名称")
  private String logisticsName;

  @Schema(title = "物流商唯一代号")
  private String slug;

}
