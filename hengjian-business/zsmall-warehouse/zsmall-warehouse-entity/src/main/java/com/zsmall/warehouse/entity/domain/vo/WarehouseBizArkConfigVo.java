package com.zsmall.warehouse.entity.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.warehouse.entity.domain.WarehouseBizArkConfig;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;



/**
 * 第三方仓库-恒健仓库配置视图对象 warehouse_bizark_config
 *
 * <AUTHOR>
 * @date 2023-06-17
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WarehouseBizArkConfig.class)
public class WarehouseBizArkConfigVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 恒健API地址
     */
    @ExcelProperty(value = "恒健API地址")
    private String bizArkApiUrl;

    /**
     * 恒健密钥
     */
    @ExcelProperty(value = "恒健密钥")
    private String bizArkSecretKey;

    /**
     * 恒健渠道账户
     */
    @ExcelProperty(value = "恒健渠道账户")
    private String bizArkChannelAccount;


}
