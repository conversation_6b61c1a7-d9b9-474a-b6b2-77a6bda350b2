package com.zsmall.warehouse.entity.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 基础信息-仓库信息
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
@Schema(name = "响应参数-仓库查询marketplace参数")
public class RespWarehouseBody {

  @Schema(title = "仓库系统编码")
  private String warehouseID;

  @Schema(title = "仓库服务商名称")
  private String warehouseName;

  @Schema(title = "国家")
  private String country_zh_CN;

  @Schema(title = "国家")
  private String country_en_US;

  @Schema(title = "州/省")
  private String state_zh_CN;

  @Schema(title = "州/省")
  private String state_en_US;

  @Schema(title = "城市")
  private String city;

  @Schema(title = "州/省（手输）")
  private String stateName;

  @Schema(title = "仓库地址1")
  private String address1;

  @Schema(title = "仓库地址2")
  private String address2;

  @Schema(title = "zipCode")
  private String zipCode;

  @Schema(title = "库存")
  private Integer stock;

}
