package com.zsmall.warehouse.entity.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/1/19
 **/
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "物流服务商信息")
public class RespShippingServiceBody {

    @Schema(title = "服务商编码")
    private String shippingCode;

    @Schema(title = "服务商名称")
    private String shippingName;

}
