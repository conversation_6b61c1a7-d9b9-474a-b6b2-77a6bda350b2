package com.zsmall.warehouse.entity.domain.vo;

import com.zsmall.common.domain.dto.StoreWarehouseBody;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 响应信息-仓库列表
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "响应信息-仓库列表")
public class RespGetWarehouseListBody {

  @Schema(title = "仓库列表")
  private List<StoreWarehouseBody> warehouseList;

}
