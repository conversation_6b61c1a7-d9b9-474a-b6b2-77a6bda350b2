package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 仓库key配置
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "仓库key配置")
public class WarehouseKeyConfigBody {

    @Schema(title = "仓库服务商类型")
    private String warehouseType;

    @Schema(title = "key1")
    private String key1;

    @Schema(title = "key2")
    private String key2;

    @Schema(title = "key3")
    private String key3;

    @Schema(title = "是否是编辑模式")
    private boolean isEdit = false;

}
