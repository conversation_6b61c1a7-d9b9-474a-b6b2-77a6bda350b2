package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 响应信息-仓库key集合
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "响应信息-仓库key集合")
public class RespKeyConfigListBody {

    @Schema(title = "可配置的仓库服务商")
    private List<String> warehouseTypes;

    @Schema(title = "仓库key集合")
    private List<WarehouseKeyConfigBody> keyConfigBodies;

}
