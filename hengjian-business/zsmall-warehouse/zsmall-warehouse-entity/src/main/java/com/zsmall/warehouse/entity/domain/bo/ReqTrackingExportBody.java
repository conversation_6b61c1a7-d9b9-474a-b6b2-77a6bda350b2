package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 请求参数-导出跟踪单
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(name = "请求参数-导出跟踪单")
@EqualsAndHashCode(callSuper = false)
public class ReqTrackingExportBody {

    @Schema(title = "订单状态")
    private String status;

    @Schema(title = "搜索类型：OrderNo-MD订单号，ChannelOrderName-渠道订单号，ItemNo，Sku，TrackingNo-跟踪单号")
    private String queryType;

    @Schema(title = "搜索值")
    private String queryValue;

    @Schema(title = "发货方式：PickUp-自提，DropShipping-代发")
    private String logisticsType;

    @Schema(title = "物流商名称")
    private String logisticsName;

    @Schema(title = "物流状态")
    private String logisticsStatus;

    @Schema(title = "销售通道")
    private String salesChannel;

    @Schema(title = "订单日期-起始")
    private String startDate;

    @Schema(title = "订单日期-结束")
    private String endDate;

}
