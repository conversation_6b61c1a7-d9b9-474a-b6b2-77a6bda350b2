package com.zsmall.warehouse.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.domain.WarehouseDeliveryCountry;
import com.zsmall.warehouse.entity.mapper.WarehouseDeliveryCountryMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class IWarehouseDeliveryCountryService extends ServiceImpl<WarehouseDeliveryCountryMapper, WarehouseDeliveryCountry> {
    public List<Long> getByCountryCodeAndWarehouseId(List<Long> warehouseIds, String countryCode) {
        LambdaQueryWrapper<WarehouseDeliveryCountry> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WarehouseDeliveryCountry::getCountryCode, countryCode);
        queryWrapper.in(WarehouseDeliveryCountry::getWarehouseId, warehouseIds);
        List<WarehouseDeliveryCountry> list = list(queryWrapper);

        if(CollUtil.isNotEmpty(list)){
            return list.stream().map(WarehouseDeliveryCountry::getWarehouseId).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 功能描述：筛选仓库id
     *
     * @param list1       列表1
     * @param countryCode 国家代码
     * @return {@link List }<{@link String }>
     * <AUTHOR>
     * @date 2025/03/07
     */
    public List<String> filterWarehouseIds(List<Warehouse> list1, String countryCode) {

        List<Long> warehouseIds = new ArrayList<>();
        warehouseIds = list1.stream().map(Warehouse::getId)
                            .collect(Collectors.toList());
        List<Long> filterWarehouseIds = getByCountryCodeAndWarehouseId(warehouseIds, countryCode);
        List<String> warehouseCodes = list1.stream()
                                    .filter(warehouse -> filterWarehouseIds.contains(warehouse.getId()))
                                    .map(Warehouse::getWarehouseCode)
                                    .collect(Collectors.toList());
        return warehouseCodes;
    }
}
