package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/2/11
 **/
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
@Schema(name = "请求信息-物流模板列表信息（商品详情用）")
public class ReqLogisticsTemplate2ProductBody {

    @Schema(title = "仓库code")
    private String warehouseCode;

    @Schema(title = "仓库系统编号")
    private String warehouseSystemCode;
}
