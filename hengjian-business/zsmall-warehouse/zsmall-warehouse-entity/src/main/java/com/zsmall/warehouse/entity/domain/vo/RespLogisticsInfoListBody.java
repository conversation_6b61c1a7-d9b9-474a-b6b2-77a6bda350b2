package com.zsmall.warehouse.entity.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 第三方物流商信息
 * <AUTHOR>
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
@Schema(name = "第三方物流商信息集合")
public class RespLogisticsInfoListBody {

  @Schema(title = "物流商名称集合")
  private List<String> logisticsNames;

}
