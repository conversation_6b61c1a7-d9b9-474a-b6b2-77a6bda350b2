package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/1/19
 **/
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "新增物流服务商参数")
public class ReqShippingServiceAddBody {

    @Schema(title = "服务商名称")
    private String shippingName;

    @Schema(title = "服务商唯一代号")
    private String shippingService;

}
