package com.zsmall.warehouse.entity.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 基础信息-仓库信息
 * <AUTHOR>
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
@Schema(name = "响应参数-仓库查询marketplace参数")
public class RespWarehouseListBody {

  @Schema(title = "仓库系统编码")
  private List<RespWarehouseBody> warehouseList;

}
