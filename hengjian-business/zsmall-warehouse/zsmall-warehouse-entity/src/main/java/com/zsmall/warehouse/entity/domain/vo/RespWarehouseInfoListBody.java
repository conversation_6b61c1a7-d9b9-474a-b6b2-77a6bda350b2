package com.zsmall.warehouse.entity.domain.vo;

import com.zsmall.common.domain.dto.StoreWarehouseBody;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 响应体-仓库列表信息
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
@Schema(name = "响应体-仓库列表信息")
public class RespWarehouseInfoListBody {

    @Schema(title = "货源类型")
    private String productSourceType;

    @Schema(title = "总数")
    private Long total;

    @Schema(title = "总页数")
    private Integer totalPage;

    @Schema(title = "结果集")
    private List<StoreWarehouseBody> results;

}
