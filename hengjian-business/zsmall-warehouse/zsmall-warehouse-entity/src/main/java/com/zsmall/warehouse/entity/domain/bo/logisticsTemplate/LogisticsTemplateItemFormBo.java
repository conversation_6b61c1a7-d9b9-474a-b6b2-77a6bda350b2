package com.zsmall.warehouse.entity.domain.bo.logisticsTemplate;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 请求体：物流模板服务费用详情参数
 **/
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class LogisticsTemplateItemFormBo {

    /**
     * 模板详情id，编辑时传入
     */
    private Long templateItemId;

    /**
     * 最快送达时间
     */
    private Integer fastestDeliveryTime;

    /**
     * 最慢送达时间
     */
    private Integer slowestDeliveryTime;

    /**
     * 目的地州列表
     */
    private List<LogisticsTemplateShipToFormBo> shipToBodyList;

}
