package com.zsmall.warehouse.entity.domain.vo.warehouse;

import lombok.Data;

import java.io.Serializable;

/**
 * 仓库新增/编辑完整信息对象
 *
 * <AUTHOR>
 * @date 2023-05-22
 */
@Data
public class WarehouseIntactInfoVo implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 仓库类型
     */
    private String warehouseType;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库编号
     */
    private String warehouseCode;

    /**
     * 仓库唯一系统编号
     */
    private String warehouseSystemCode;

    /**
     * 是否支持第三方物流账号
     */
    private Boolean supportLogisticsAccount;

    /**
     * 仓库邮编
     */
    private String zipCode;

    /**
     * 国家
     */
    private Long countryId;

    /**
     * 国家
     */
    private String country;

    /**
     * 州/省/地区
     */
    private Long stateId;

    /**
     * 州/省/地区
     */
    private String state;

    /**
     * 城市
     */
    private String city;

    /**
     * 街道、公司等
     */
    private String address1;

    /**
     * 楼/座、楼层、房号等
     */
    private String address2;

    /**
     * 仓管姓名
     */
    private String managerName;

    /**
     * 仓管电话
     */
    private String managerPhone;


}
