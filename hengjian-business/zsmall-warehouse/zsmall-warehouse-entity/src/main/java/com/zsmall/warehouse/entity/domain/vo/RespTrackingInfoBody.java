package com.zsmall.warehouse.entity.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 响应信息-跟踪信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(name = "响应信息-跟踪信息")
@EqualsAndHashCode(callSuper = false)
public class RespTrackingInfoBody {

    @Schema(title = "MD订单时间")
    private String orderTime;

    @Schema(title = "渠道订单时间")
    private String orderChannelTime;

    @Schema(title = "订单编号")
    private String orderId;

    @Schema(title = "渠道类型")
    private String channelType;

    @Schema(title = "渠道订单编号")
    private String channelOrderId;

    @Schema(title = "渠道店铺名")
    private String channelAlias;

    @Schema(title = "订单状态")
    private String orderStatus;

    @Schema(title = "商品：分销商时传的是Item No.，供货商时传的是sku")
    private String product;

    @Schema(title = "商品数量")
    private String quantity;

    @Schema(title = "发货方式：PickUp-自提，DropShipping-代发")
    private String logisticsType;

    @Schema(title = "物流商名称")
    private String logisticsName;

    @Schema(title = "物流服务")
    private String logisticsService;

    @Schema(title = "跟踪单号")
    private String trackingNo;

    @Schema(title = "物流状态")
    private String logisticsStatus;

    @Schema(title = "第三方物流API查询Code")
    private String thirdPartyCode;

    @Schema(title = "第三方物流API查询信息")
    private String thirdPartyMessage;

    @Schema(title = "第三方物流API更新时间")
    private String thirdPartyDateTime;

    @Schema(title = "物流跟踪单查询重定向地址")
    private String trackingRedirectUrl;

}
