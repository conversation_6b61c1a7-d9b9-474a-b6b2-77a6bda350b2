package com.zsmall.warehouse.entity.mapper;

import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.warehouse.entity.domain.WarehouseDeliveryCountry;
import com.zsmall.warehouse.entity.domain.vo.WarehouseDeliveryCountryVo;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 供应商仓库支持配送国家Mapper接口
 *
 * <AUTHOR> Li
 * @date 2024-12-19
 */
public interface WarehouseDeliveryCountryMapper extends BaseMapperPlus<WarehouseDeliveryCountry, WarehouseDeliveryCountryVo> {

    List<WarehouseDeliveryCountry> getWarehouseDeliveryCountryByWarehouseCodes(@Param("warehouseCode") String warehouseCode);

    void removeByWarehouseId(@Param("warehouseId") Long warehouseId);
}
