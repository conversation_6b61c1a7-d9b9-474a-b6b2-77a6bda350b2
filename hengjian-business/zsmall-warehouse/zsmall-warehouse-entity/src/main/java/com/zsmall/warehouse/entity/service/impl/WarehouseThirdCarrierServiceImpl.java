package com.zsmall.warehouse.entity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.warehouse.entity.domain.WarehouseThirdCarrierEntity;
import com.zsmall.warehouse.entity.domain.enums.CarrierTypeEnum;
import com.zsmall.warehouse.entity.mapper.WarehouseThirdCarrierMapper;
import com.zsmall.warehouse.entity.service.WarehouseThirdCarrierService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【warehouse_third_carrier(仓库第三方物流商)】的数据库操作Service实现
* @createDate 2022-07-15 15:57:11
*/
@Service
@Slf4j
public class WarehouseThirdCarrierServiceImpl extends ServiceImpl<WarehouseThirdCarrierMapper, WarehouseThirdCarrierEntity>
    implements WarehouseThirdCarrierService {

  @Override
  public List<WarehouseThirdCarrierEntity> getByWarehouseId(Long warehouseId) {
    log.info("进入【根据仓库id获取第三发货商信息】方法, warehouseEntityId = {}", warehouseId);
    LambdaQueryWrapper<WarehouseThirdCarrierEntity> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(WarehouseThirdCarrierEntity::getWarehouseId, warehouseId);
    return this.list(queryWrapper);
  }

  @Override
  public WarehouseThirdCarrierEntity getByWarehouseIdAndType(Long warehouseId, CarrierTypeEnum carrierTypeEnum) {
    log.info("进入【根据仓库id和第三方物流商，获取第三方物流商信息】方法, warehouseEntityId = {}", warehouseId);
    LambdaQueryWrapper<WarehouseThirdCarrierEntity> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(WarehouseThirdCarrierEntity::getWarehouseId, warehouseId)
      .eq(WarehouseThirdCarrierEntity::getCarrierType, carrierTypeEnum.name());
    return this.getOne(queryWrapper);
  }
}




