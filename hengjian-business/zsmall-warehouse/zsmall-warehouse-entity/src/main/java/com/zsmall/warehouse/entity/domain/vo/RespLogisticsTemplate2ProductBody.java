package com.zsmall.warehouse.entity.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/1/27
 **/
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
@Schema(name = "响应信息-物流模板列表信息（商品详情用）")
public class RespLogisticsTemplate2ProductBody {

  @Schema(title = "物流模板名称")
  private String logisticsTemplateName;

  @Schema(title = "物流模板编号")
  private String logisticsTemplateNo;
}
