package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/1/19
 **/
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "删除物流模板服务费用详情参数")
public class ReqLogisticsTemplateItemDelBody {

    @Schema(title = "模板详情id")
    private Long templateItemId;

}
