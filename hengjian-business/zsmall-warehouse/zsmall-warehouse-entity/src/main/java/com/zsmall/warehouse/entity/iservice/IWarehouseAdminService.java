package com.zsmall.warehouse.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.warehouse.entity.domain.WarehouseAdminInfo;
import com.zsmall.warehouse.entity.mapper.WarehouseAdminInfoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年7月10日  17:38
 * @description: 管理远仓库业务处理
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IWarehouseAdminService extends ServiceImpl<WarehouseAdminInfoMapper, WarehouseAdminInfo> {

    private final WarehouseAdminInfoMapper baseMapper;

    /**
     * 获取全部的管理员仓库信息
     *
     * @return
     */
    public List<WarehouseAdminInfo> listWarehouseAdminInfo(){
        LambdaQueryWrapper<WarehouseAdminInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(WarehouseAdminInfo::getDelFlag, "0");
        lqw.eq(WarehouseAdminInfo::getWarehouseState, 1);
        lqw.orderByDesc(WarehouseAdminInfo::getId);
        return TenantHelper.ignore(() ->baseMapper.selectList(lqw));
    }

}
