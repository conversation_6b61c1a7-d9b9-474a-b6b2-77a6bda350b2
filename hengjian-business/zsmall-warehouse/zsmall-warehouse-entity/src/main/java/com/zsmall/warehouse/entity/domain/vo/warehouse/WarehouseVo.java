package com.zsmall.warehouse.entity.domain.vo.warehouse;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.common.enums.warehouse.WarehouseTypeEnum;
import com.zsmall.warehouse.entity.domain.Warehouse;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;



/**
 * 仓库管理视图对象 warehouse
 *
 * <AUTHOR>
 * @date 2023-05-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Warehouse.class)
public class WarehouseVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 仓库类型
     */
    @ExcelProperty(value = "仓库类型")
    private WarehouseTypeEnum warehouseType;

    /**
     * 仓库名称
     */
    @ExcelProperty(value = "仓库名称")
    private String warehouseName;

    /**
     * 仓库编号
     */
    @ExcelProperty(value = "仓库编号")
    private String warehouseCode;

    /**
     * 仓库唯一系统编号
     */
    @ExcelProperty(value = "仓库唯一系统编号")
    private String warehouseSystemCode;

    /**
     * 支持第三方物流账户
     */
    @ExcelProperty(value = "支持第三方物流账户")
    private Boolean supportLogisticsAccount;

    /**
     * 仓库邮编
     */
    @ExcelProperty(value = "仓库邮编")
    private String zipCode;

    /**
     * 所处国家二位代号
     */
    @ExcelProperty(value = "所处国家二位代号")
    private String country;

    /**
     * 是否支持第三方仓库
     */
    @ExcelProperty(value = "是否支持第三方仓库", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "true_false")
    private Boolean isSupportThirdCarrier;

    /**
     * 仓库状态
     */
    @ExcelIgnore
    private Integer warehouseState;
    /**
     * 仓库地址信息
     */
    @ExcelIgnore
    private WarehouseAddressVo warehouseAddressVo;
}
