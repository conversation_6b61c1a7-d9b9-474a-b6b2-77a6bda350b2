package com.zsmall.warehouse.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 第三方仓库-恒健仓库配置对象 warehouse_bizark_config
 *
 * <AUTHOR>
 * @date 2023-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("warehouse_biz_ark_config")
public class WarehouseBizArkConfig extends NoDeptTenantEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 关联的仓库唯一系统编号
     */
    private String warehouseSystemCode;

    /**
     * 恒健API地址
     */
    private String bizArkApiUrl;

    /**
     * 恒健密钥
     */
    private String bizArkSecretKey;

    /**
     * 恒健渠道账户
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String bizArkChannelAccount;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
