package com.zsmall.warehouse.entity.mapper;


import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.warehouse.entity.domain.WarehouseAdminDeliveryCountry;
import com.zsmall.warehouse.entity.domain.vo.warehouse.WarehouseAdminDeliveryCountryVo;
import org.apache.ibatis.annotations.Param;

import javax.validation.constraints.NotNull;

/**
 * 仓库地址支持配送国家Mapper接口
 *
 * <AUTHOR> Li
 * @date 2024-12-17
 */
public interface WarehouseAdminDeliveryCountryMapper extends BaseMapperPlus<WarehouseAdminDeliveryCountry, WarehouseAdminDeliveryCountryVo> {

    void removeByAdminId(@Param("adminId") Long id);
}
