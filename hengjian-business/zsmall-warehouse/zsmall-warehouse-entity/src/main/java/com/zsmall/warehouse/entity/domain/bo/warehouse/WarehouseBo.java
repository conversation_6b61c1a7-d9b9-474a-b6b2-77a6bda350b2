package com.zsmall.warehouse.entity.domain.bo.warehouse;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.warehouse.entity.domain.Warehouse;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 仓库管理业务对象 warehouse
 *
 * <AUTHOR>
 * @date 2023-05-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Warehouse.class, reverseConvertGenerate = false)
public class WarehouseBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 仓库类型
     */
    private String warehouseType;

    /**
     * 仓库名称
     */
    @NotBlank(message = "仓库名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String warehouseName;

    /**
     * 仓库编号
     */
    @NotBlank(message = "仓库编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String warehouseCode;

    /**
     * 仓库唯一系统编号
     */
    private String warehouseSystemCode;

    /**
     * 仓库状态
     */
    @NotNull(message = "仓库状态不能为空", groups = { EditGroup.class })
    private Integer warehouseState;

    /**
     * 仓库邮编
     */
    @NotBlank(message = "仓库邮编不能为空", groups = { AddGroup.class, EditGroup.class })
    private String zipCode;

    /**
     * 所处国家二位代号
     */
    private String country;

    private String tenantType;

    private String tenantId;


}
