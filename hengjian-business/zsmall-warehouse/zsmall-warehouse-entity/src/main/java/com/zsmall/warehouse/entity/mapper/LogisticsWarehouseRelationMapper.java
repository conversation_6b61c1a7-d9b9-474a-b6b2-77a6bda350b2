package com.zsmall.warehouse.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zsmall.warehouse.entity.domain.LogisticsWarehouseRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【logistics_warehouse_relation】的数据库操作Mapper
* @createDate 2023-05-06 12:02:03
* @Entity generator.domain.LogisticsWarehouseRelation
*/
public interface LogisticsWarehouseRelationMapper extends BaseMapper<LogisticsWarehouseRelation> {

    @InterceptorIgnore(tenantLine = "true")
    List<String> queryWarehouseName(@Param("logisticsTemplateId") Long logisticsTemplateId);

}




