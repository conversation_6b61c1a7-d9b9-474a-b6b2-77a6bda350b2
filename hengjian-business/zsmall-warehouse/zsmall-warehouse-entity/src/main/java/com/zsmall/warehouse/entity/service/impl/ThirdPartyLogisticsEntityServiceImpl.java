package com.zsmall.warehouse.entity.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.common.enums.EffectiveStateType;
import com.zsmall.warehouse.entity.domain.ThirdPartyLogisticsEntity;
import com.zsmall.warehouse.entity.mapper.ThirdPartyLogisticsEntityMapper;
import com.zsmall.warehouse.entity.service.ThirdPartyLogisticsEntityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【third_party_logistics(第三方物流信息表)】的数据库操作Service实现
* @createDate 2023-05-06 12:24:20
*/
@Slf4j
@Service
public class ThirdPartyLogisticsEntityServiceImpl extends ServiceImpl<ThirdPartyLogisticsEntityMapper, ThirdPartyLogisticsEntity>
    implements ThirdPartyLogisticsEntityService{


    @Override
    public ThirdPartyLogisticsEntity findBySlugAndStateType(String slug, EffectiveStateType stateType) {
        log.info("进入【根据物流商唯一代号和状态查询第三方物流信息】方法");
        return lambdaQuery().eq(ThirdPartyLogisticsEntity::getSlug, slug)
            .eq(ThirdPartyLogisticsEntity::getStatusType, stateType)
            .one();
    }

    @Override
    public List<ThirdPartyLogisticsEntity> findByStateTypeOrderByNameAsc(EffectiveStateType stateType) {
        log.info("进入【根据状态查询第三方物流信息列表】方法");
        return lambdaQuery().eq(ThirdPartyLogisticsEntity::getStatusType, stateType)
            .orderByAsc(ThirdPartyLogisticsEntity::getName)
            .list();
    }

    @Override
    public String getSlugByName(String name, EffectiveStateType stateType) {
        log.info("进入【根据子模板id和状态 最小区域和最小重量排序筛选列表】方法");
        return lambdaQuery().eq(ThirdPartyLogisticsEntity::getName, name)
            .eq(ThirdPartyLogisticsEntity::getStatusType, stateType)
            .one().getSlug();
    }
}




