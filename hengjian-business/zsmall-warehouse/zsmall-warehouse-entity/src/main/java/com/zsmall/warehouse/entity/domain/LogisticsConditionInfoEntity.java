package com.zsmall.warehouse.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 物流子模块信息(物流条件)表
 * @TableName logistics_condition_info
 */
@TableName(value ="logistics_condition_info")
@Data
@EqualsAndHashCode(callSuper=false)
public class LogisticsConditionInfoEntity extends SortEntity implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 高
     */
    private BigDecimal height;

    /**
     * 长
     */
    private BigDecimal length;

    /**
     * 子模板ID
     */
    private Long logisticsConditionId;

    /**
     * 最大重量
     */
    private BigDecimal maxWeight;

    /**
     * 最大区域
     */
    private Long maxZone;

    /**
     * 最小重量
     */
    private BigDecimal minWeight;

    /**
     * 最小区域
     */
    private Long minZone;

    /**
     * 运费价格
     */
    private BigDecimal shippingCost;

    /**
     * 有效状态
     */
    private String statusType;

    /**
     * 宽
     */
    private BigDecimal width;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
