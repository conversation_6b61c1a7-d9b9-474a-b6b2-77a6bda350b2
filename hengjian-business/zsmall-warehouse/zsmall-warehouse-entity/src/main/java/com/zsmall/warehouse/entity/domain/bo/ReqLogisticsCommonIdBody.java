package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 请求参数-物流管理查询和删除通用id
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "请求参数-物流管理查询和删除通用id")
public class ReqLogisticsCommonIdBody {

    @Schema(title = "id")
    private Long id;

    @Schema(title = "是否员工创建")
    private Boolean isDepot;
}
