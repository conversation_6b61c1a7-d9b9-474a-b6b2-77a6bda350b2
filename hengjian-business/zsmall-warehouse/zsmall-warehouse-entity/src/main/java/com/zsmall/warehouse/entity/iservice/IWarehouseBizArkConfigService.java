package com.zsmall.warehouse.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.warehouse.entity.domain.WarehouseBizArkConfig;
import com.zsmall.warehouse.entity.mapper.WarehouseBizArkConfigMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 第三方仓库-恒健仓库配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-17
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class IWarehouseBizArkConfigService extends ServiceImpl<WarehouseBizArkConfigMapper, WarehouseBizArkConfig> {

    /**
     * 根据仓库系统编号查询
     * @param warehouseSystemCode
     * @return
     */
    public WarehouseBizArkConfig queryByWarehouseSystemCode(String warehouseSystemCode) {
        log.info("进入【根据仓库系统编号查询】 warehouseSystemCode = {}", warehouseSystemCode);
        return TenantHelper.ignore(() -> lambdaQuery().eq(WarehouseBizArkConfig::getWarehouseSystemCode, warehouseSystemCode).one());
    }

    /**
     * 根据供货商租户编号编号查询
     * @param supplierId 供货商租户编号
     * @return
     */
    public WarehouseBizArkConfig queryBySupplierId(String supplierId) {
        log.info("进入【根据供货商租户编号编号查询】 warehouseSystemCode = {}", supplierId);
        return TenantHelper.ignore(() -> lambdaQuery().eq(WarehouseBizArkConfig::getTenantId, supplierId).one());
    }

}
