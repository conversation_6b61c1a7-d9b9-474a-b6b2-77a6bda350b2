package com.zsmall.warehouse.entity.domain.bo.warehouse;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.warehouse.entity.domain.Warehouse;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 仓库新增/编辑完整信息对象
 *
 * <AUTHOR>
 * @date 2023-05-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Warehouse.class, reverseConvertGenerate = false)
public class WarehouseIntactInfoBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 仓库类型
     */
    private String warehouseType;

    /**
     * 仓库名称
     */
    @NotBlank(message = "仓库名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String warehouseName;

    /**
     * 仓库编号
     */
    @NotBlank(message = "仓库编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String warehouseCode;

    /**
     * 仓库唯一系统编号
     */
    private String warehouseSystemCode;

    /**
     * 是否支持第三方物流账号
     */
    private Boolean supportLogisticsAccount;

    /**
     * 仓库邮编
     */
    @NotBlank(message = "仓库邮编不能为空", groups = { AddGroup.class, EditGroup.class })
    private String zipCode;

    /**
     * 国家
     */
    @NotNull(message = "国家不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long countryId;

    /**
     * 国家
     */
    // @NotBlank(message = "国家不能为空", groups = { AddGroup.class, EditGroup.class })
    private String country;

    /**
     * 州/省/地区
     */
    // @NotBlank(message = "州/省/地区不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long stateId;

    /**
     * 州/省/地区
     */
    // @NotBlank(message = "州/省/地区不能为空", groups = { AddGroup.class, EditGroup.class })
    private String state;

    /**
     * 城市
     */
    @NotBlank(message = "城市不能为空", groups = { AddGroup.class, EditGroup.class })
    private String city;

    /**
     * 街道、公司等
     */
    @NotBlank(message = "街道、公司等不能为空", groups = { AddGroup.class, EditGroup.class })
    private String address1;

    /**
     * 楼/座、楼层、房号等
     */
    private String address2;

    /**
     * 仓管姓名
     */
    @NotBlank(message = "仓管姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String managerName;

    /**
     * 仓管电话
     */
    @NotBlank(message = "仓管电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String managerPhone;

    /**
     * 恒健API地址
     */
    private String bizArkApiUrl;

    /**
     * 恒健密钥
     */
    private String bizArkSecretKey;

    /**
     * 恒健渠道账户
     */
    private String bizArkChannelAccount;

}
