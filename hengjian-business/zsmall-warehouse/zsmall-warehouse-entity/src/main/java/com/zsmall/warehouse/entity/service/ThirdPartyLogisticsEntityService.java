package com.zsmall.warehouse.entity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zsmall.common.enums.EffectiveStateType;
import com.zsmall.warehouse.entity.domain.ThirdPartyLogisticsEntity;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【third_party_logistics(第三方物流信息表)】的数据库操作Service
* @createDate 2023-05-06 12:24:20
*/
public interface ThirdPartyLogisticsEntityService extends IService<ThirdPartyLogisticsEntity> {

    /**
     * 根据物流商唯一代号和状态查询第三方物流信息
     * @param slug
     * @param stateType
     * @return
     */
    ThirdPartyLogisticsEntity findBySlugAndStateType(String slug, EffectiveStateType stateType);

    List<ThirdPartyLogisticsEntity> findByStateTypeOrderByNameAsc(EffectiveStateType stateType);

    String getSlugByName(@Param("name") String name, @Param("stateType") EffectiveStateType stateType);

}
