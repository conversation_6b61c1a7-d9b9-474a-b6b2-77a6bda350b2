package com.zsmall.warehouse.entity.domain.vo.warehouse;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.system.entity.domain.WorldLocation;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年5月27日  17:04
 * @description: 管理员仓库excel导入Vo
 */
@Data
public class AdminWarehouseExcelImportVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "*仓库名称（不允许重复）")
    private String warehouseName;
    @ExcelProperty(value = "*仓库编号（不允许重复）")
    private String warehouseCode;
    @ExcelProperty(value = "*国家/地区")
    private String country;
    @ExcelProperty(value = "*州/省/地区")
    private String state;
    @ExcelProperty(value = "*城市")
    private String city;
    @ExcelProperty(value = "*街道地址")
    private String address1;
    @ExcelProperty(value = "*详细地址")
    private String address2;
    @ExcelProperty(value = "*邮编")
    private String zipCode;
    @ExcelProperty(value = "仓管姓名")
    private String managerName;
    @ExcelProperty(value = "仓管电话")
    private String managerPhone;

    /**
     * 支持配送国家
     */
    @ExcelProperty(value = "*支持配送国家（请填写全称，多个国家用英文逗号分隔）")
    private String deliveryCountry;

    @ExcelIgnore
    private List<WorldLocation> worldLocationList;

    /**
     * 地区表主键（国家）
     */
    @ExcelIgnore
    private Long countryId;

    /**
     * 地区表主键（州/省）
     */
    @ExcelIgnore
    private Long stateId;
}
