package com.zsmall.warehouse.entity.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 通用参数-其他物流
 * <AUTHOR>
 * @create 2022/3/16 11:59
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "通用参数-其他物流")
public class OtherLogisticsBody {

  @Schema(title = "物流跟踪单号")
  private String trackingNo;

  @Schema(title = "物流标签存放地址")
  private String labelSavePath;

}
