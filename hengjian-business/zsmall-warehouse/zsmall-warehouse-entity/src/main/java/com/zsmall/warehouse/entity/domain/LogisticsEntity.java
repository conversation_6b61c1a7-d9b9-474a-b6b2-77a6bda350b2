package com.zsmall.warehouse.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 物流表
 * @TableName logistics
 */
@TableName(value ="logistics")
@Data
@EqualsAndHashCode(callSuper=false)
public class LogisticsEntity extends SortEntity implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 模板名称
     */
    private String logisticsName;

    /**
     * 国家主键
     */
    private Long countryId;

    /**
     * 有效状态
     */
    private String statusType;

    /**
     * 商户主键
     */
    private Long storeId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
