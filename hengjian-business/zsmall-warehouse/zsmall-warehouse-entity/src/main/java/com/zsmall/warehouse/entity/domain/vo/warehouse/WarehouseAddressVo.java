package com.zsmall.warehouse.entity.domain.vo.warehouse;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.warehouse.entity.domain.WarehouseAddress;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;



/**
 * 仓库地址信息视图对象 warehouse_address
 *
 * <AUTHOR>
 * @date 2023-05-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WarehouseAddress.class)
public class WarehouseAddressVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 仓库主表主键
     */
    @ExcelProperty(value = "仓库主表主键")
    private Long warehouseId;

    /**
     * 仓库唯一系统编号
     */
    @ExcelProperty(value = "仓库唯一系统编号")
    private String warehouseSystemCode;

    /**
     * 地区表主键（国家）
     */
    @ExcelProperty(value = "地区表主键", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "国=家")
    private Long countryId;

    /**
     * 国家名文本（key-语种，value-对应语种名称）
     */
    @ExcelProperty(value = "国家名文本", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "k=ey-语种，value-对应语种名称")
    private String country;

    /**
     * 地区表主键（州/省）
     */
    @ExcelProperty(value = "地区表主键", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "州=/省")
    private Long stateId;

    /**
     * 州/省名文本（key-语种，value-对应语种名称）
     */
    @ExcelProperty(value = "州/省名文本", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "k=ey-语种，value-对应语种名称")
    private String state;

    /**
     * 地区表主键（市县）
     */
    @ExcelProperty(value = "地区表主键", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "市=县")
    private Long cityId;

    /**
     * 市县名文本（key-语种，value-对应语种名称）
     */
    @ExcelProperty(value = "市县名文本", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "k=ey-语种，value-对应语种名称")
    private String city;

    /**
     * 详细地址1
     */
    @ExcelProperty(value = "详细地址1")
    private String address1;

    /**
     * 详细地址2
     */
    @ExcelProperty(value = "详细地址2")
    private String address2;

    /**
     * 仓库邮编
     */
    @ExcelProperty(value = "仓库邮编")
    private String zipCode;

    /**
     * 仓库管理者姓名
     */
    @ExcelProperty(value = "仓库管理者姓名")
    private String managerName;

    /**
     * 仓库管理者联系电话
     */
    @ExcelProperty(value = "仓库管理者联系电话")
    private String managerPhone;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private Long longitude;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private Long latitude;


}
