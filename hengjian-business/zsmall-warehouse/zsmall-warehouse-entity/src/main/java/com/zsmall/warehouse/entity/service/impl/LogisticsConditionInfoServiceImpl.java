package com.zsmall.warehouse.entity.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.common.enums.EffectiveStateType;
import com.zsmall.warehouse.entity.domain.LogisticsConditionInfoEntity;
import com.zsmall.warehouse.entity.mapper.LogisticsConditionInfoMapper;
import com.zsmall.warehouse.entity.service.LogisticsConditionInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【logistics_condition_info(物流子模块信息(物流条件)表)】的数据库操作Service实现
* @createDate 2023-05-06 12:02:03
*/
@Slf4j
@Service
public class LogisticsConditionInfoServiceImpl extends ServiceImpl<LogisticsConditionInfoMapper, LogisticsConditionInfoEntity>
    implements LogisticsConditionInfoService{

    @Override
    public List<LogisticsConditionInfoEntity> findByLogisticsConditionIdAndStatusTypeOrderByMinZoneAscMinWeightAsc(Long id, EffectiveStateType effectiveStateType) {
        log.info("进入【根据子模板id和状态 最小区域和最小重量排序筛选列表】方法");
        return lambdaQuery().eq(LogisticsConditionInfoEntity::getId, id)
            .eq(LogisticsConditionInfoEntity::getStatusType, effectiveStateType)
            .orderByAsc(LogisticsConditionInfoEntity::getMinZone)
            .orderByAsc(LogisticsConditionInfoEntity::getMinWeight)
            .list();
    }

    @Override
    public List<LogisticsConditionInfoEntity> findByIdIn(List<Long> ids) {
        log.info("进入【根据ids查询条件信息】方法");
        return lambdaQuery().in(LogisticsConditionInfoEntity::getId, ids).list();
    }
}




