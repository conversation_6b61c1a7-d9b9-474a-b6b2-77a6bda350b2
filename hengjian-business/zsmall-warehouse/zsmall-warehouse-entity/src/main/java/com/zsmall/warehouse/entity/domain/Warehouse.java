package com.zsmall.warehouse.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.warehouse.WarehouseTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 仓库管理对象 warehouse
 *
 * <AUTHOR>
 * @date 2023-05-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("warehouse")
public class Warehouse extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 仓库类型
     */
    private WarehouseTypeEnum warehouseType;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库编号
     */
    private String warehouseCode;

    /**
     * 仓库唯一系统编号
     */
    private String warehouseSystemCode;

    /**
     * 仓库状态
     */
    private Integer warehouseState = 1;

    /**
     * 仓库邮编
     */
    private String zipCode;

    /**
     * 所处国家二位代号
     */
    private String country;

    /**
     * 是否支持第三方物流账号
     */
    private Boolean supportLogisticsAccount;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
