package com.zsmall.warehouse.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.common.domain.SqlLocaleField;
import com.zsmall.warehouse.entity.domain.LogisticsRateCountryRelation;
import com.zsmall.warehouse.entity.mapper.LogisticsRateCountryRelationMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据库层：物流模板-物流模板费率规则-城市-关联表
 */
@Service
@Slf4j
public class ILogisticsRateCountryRelationService extends ServiceImpl<LogisticsRateCountryRelationMapper, LogisticsRateCountryRelation> {

    /**
     * 查询物流模板关联的地点名称
     * @param templateId
     * @return
     */
    public List<SqlLocaleField> queryLocationName(Long templateId) {
        return baseMapper.queryLocationName(templateId);
    }

    public List<LogisticsRateCountryRelation> getByTemplateId(Long templateId) {
        log.info("进入【根据模板id获取城市id列表】方法, templateId = {}", templateId);
        LambdaQueryWrapper<LogisticsRateCountryRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LogisticsRateCountryRelation::getLogisticsTemplateId, templateId);
        return this.list(queryWrapper);
    }

    public Long getByTemplateItemId(Long templateItemId) {
        log.info("进入【根据rateRuleId查TemplateId】方法, templateItemId = {}", templateItemId);
        Long templateId = null;
        LambdaQueryWrapper<LogisticsRateCountryRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LogisticsRateCountryRelation::getLogisticsTemplateRateRuleId, templateItemId);
        List<LogisticsRateCountryRelation> list = this.list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            templateId = list.get(0).getLogisticsTemplateId();
        }
        return templateId;
    }

    public void removeByTemplateItemId(Long templateItemId) {
        log.info("进入【根据templateItemId删除关联信息】方法, templateItemId = {}", templateItemId);
        //删除
        LambdaQueryWrapper<LogisticsRateCountryRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LogisticsRateCountryRelation::getLogisticsTemplateRateRuleId, templateItemId);
        List<LogisticsRateCountryRelation> oldList = this.list(queryWrapper);
        List<Long> ids = oldList.stream().map(LogisticsRateCountryRelation::getId).collect(Collectors.toList());
        this.removeByIds(ids);
    }

    public LogisticsRateCountryRelation getByLogisticsTemplateIdAndCountryId(Long logisticsTemplateId, Long countryId) {
        log.info("进入【根据logisticsTemplateId和countryId获取关联信息】方法， logisticsTemplateId = {}, countryId = {}", logisticsTemplateId, countryId);
        LambdaQueryWrapper<LogisticsRateCountryRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LogisticsRateCountryRelation::getLogisticsTemplateId, logisticsTemplateId)
            .eq(LogisticsRateCountryRelation::getWorldLocationId, countryId);
        return this.getOne(queryWrapper);
    }

    public List<Long> removeShipToAndGetItemIds(List<Long> itemIds, Long templateId) {
        log.info("进入【移除目的地，同时返回被删除费率规则的id】方法， itemIds = {}, templateId = {}", JSONUtil.toJsonStr(itemIds), templateId);

        List<Long> removeItemIds;
        List<LogisticsRateCountryRelation> relationList = this.getByTemplateId(templateId);
        //有保留的费率规则id
        if (CollUtil.isNotEmpty(itemIds)) {
            removeItemIds = relationList.stream()
                .filter(entity -> !CollUtil.contains(itemIds, entity.getLogisticsTemplateRateRuleId()))
                .collect(Collectors.toList())
                .stream()
                .map(LogisticsRateCountryRelation::getLogisticsTemplateRateRuleId)
                .collect(Collectors.toList());
        } else {
            //无保留
            removeItemIds = relationList.stream().map(LogisticsRateCountryRelation::getLogisticsTemplateRateRuleId).collect(Collectors.toList());
        }

        removeItemIds.forEach(this::removeByTemplateItemId);
        return removeItemIds;
    }
}




