package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 请求参数-仓库列表
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "请求参数-仓库列表")
public class ReqWarehouseListBody {

    @Schema(title = "渠道店铺名")
    private Long channelId;

    @Schema(title = "查询正文")
    private String queryContent;

    @Schema(title = "仓库服务商类型")
    private String warehouseType;

}
