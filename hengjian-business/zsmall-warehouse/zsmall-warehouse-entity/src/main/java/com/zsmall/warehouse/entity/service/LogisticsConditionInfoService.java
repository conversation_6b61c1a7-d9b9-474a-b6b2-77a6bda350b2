package com.zsmall.warehouse.entity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zsmall.common.enums.EffectiveStateType;
import com.zsmall.warehouse.entity.domain.LogisticsConditionInfoEntity;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【logistics_condition_info(物流子模块信息(物流条件)表)】的数据库操作Service
* @createDate 2023-05-06 12:02:03
*/
public interface LogisticsConditionInfoService extends IService<LogisticsConditionInfoEntity> {


    /**
     * 根据子模板id和状态 最小区域和最小重量排序筛选列表
     * @param id
     * @param effectiveStateType
     * @return
     */
    List<LogisticsConditionInfoEntity> findByLogisticsConditionIdAndStatusTypeOrderByMinZoneAscMinWeightAsc(Long id,
                                                                                                      EffectiveStateType effectiveStateType);

    /**
     * 根据ids查询条件信息
     * @param ids
     * @return
     */
    List<LogisticsConditionInfoEntity> findByIdIn(List<Long> ids);

}
