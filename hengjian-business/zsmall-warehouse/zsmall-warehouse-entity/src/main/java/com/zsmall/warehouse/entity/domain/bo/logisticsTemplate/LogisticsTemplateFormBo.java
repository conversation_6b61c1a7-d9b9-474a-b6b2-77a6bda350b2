package com.zsmall.warehouse.entity.domain.bo.logisticsTemplate;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 请求体：新增-编辑物流模板参数
 **/
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class LogisticsTemplateFormBo {

    /**
     * 模板id,编辑时传入
     */
    private Long templateId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 仓库系统编号集合
     */
    private List<String> warehouseSystemCodeList;

    /**
     * 服务编码
     */
    private String serviceCode;

    /**
     * 服务别名
     */
    private String serviceAlias;

    /**
     * 服务收费详情
     */
    private List<LogisticsTemplateItemFormBo> itemAddBodyList;

}
