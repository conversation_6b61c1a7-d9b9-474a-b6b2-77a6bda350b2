package com.zsmall.warehouse.entity.domain.bo.warehouse;

import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.zsmall.warehouse.entity.domain.WarehouseAdminInfo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;
import java.util.List;

/**
 * 管理端仓库信息业务对象 warehouse_admin_info
 *
 * <AUTHOR> Li
 * @date 2024-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WarehouseAdminInfo.class, reverseConvertGenerate = false)
public class WarehouseAdminInfoBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 仓库类型（区分自有仓库与第三方仓库）
     */
    @NotNull(message = "仓库类型不能为空", groups = { AddGroup.class})
    private Integer warehouseType;

    /**
     * 仓库类型描述
     */
    @NotBlank(message = "仓库类型描述不能为空", groups = { AddGroup.class})
    private String warehouseTypeName;

    /**
     * 仓库名称
     */
    @NotBlank(message = "仓库名称不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(min = 1, max = 2,message = "仓库名称不能超过20个字符")
    private String warehouseName;

    /**
     * 仓库编号
     */
    @NotBlank(message = "仓库编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String warehouseCode;

    /**
     * 仓库唯一系统编号
     */
    private String warehouseSystemCode;

    /**
     * 仓库状态（0-停用，1-启用等）
     */
    private Integer warehouseState;

    /**
     * 是否支持第三方物流账号（0-否，1-是）
     */
    private Integer supportLogisticsAccount;

    /**
     * 地区表主键（国家）
     */
    @NotNull(message = "地区表主键（国家）不能为空", groups = { AddGroup.class})
    private Long countryId;

    /**
     * 国家名文本
     */
    @NotBlank(message = "国家名文本不能为空", groups = { AddGroup.class})
    private String country;

    /**
     * 地区表主键（州/省）
     */
    @NotNull(message = "地区表主键（州/省）不能为空", groups = { AddGroup.class})
    private Long stateId;

    /**
     * 州/省名文本
     */
    @NotBlank(message = "州/省名文本不能为空", groups = { AddGroup.class})
    private String state;

    /**
     * 地区表主键（市县）
     */
    private Long cityId;

    /**
     * 市县名文本
     */
    @NotBlank(message = "市县名文本不能为空", groups = { AddGroup.class})
    private String city;

    /**
     * 详细地址1
     */
    @NotBlank(message = "详细地址1不能为空", groups = { AddGroup.class})
    private String address1;

    /**
     * 详细地址2
     */
    private String address2;

    /**
     * 仓库邮编
     */
    @NotBlank(message = "仓库邮编不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 10,message = "邮编长度不能超过10个字符")
    @Pattern(regexp = "^\\d+(-\\d+)?$\n",message = "邮编格式不正确")
    private String zipCode;

    /**
     * 仓库管理者姓名
     */
    private String managerName;

    /**
     * 仓库管理者联系电话
     */
    private String managerPhone;

    /**
     * 经度
     */
    private Long longitude;

    /**
     * 纬度
     */
    private Long latitude;
    /**
     * 配送国家ID
     */
    private List<Long> distributionCountriesId;

    private String tenantType;

}
