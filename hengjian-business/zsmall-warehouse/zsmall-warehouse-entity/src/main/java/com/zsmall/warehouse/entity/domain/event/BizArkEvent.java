package com.zsmall.warehouse.entity.domain.event;

import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.extend.wms.model.stock.OutStock;
import com.zsmall.warehouse.entity.domain.enums.ThirdWarehouseEventEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 第三方仓库-恒健仓库事件
 *
 * <AUTHOR>
 * @date 2023/6/17
 */
@Getter
@Setter
@Accessors(chain = true)
public class BizArkEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    private ThirdWarehouseEventEnum eventType;

    private QueryStock queryStock;

    private CreateOrder createOrder;

    public static BizArkEvent queryStock(String inSupplierId, List<String> inErpSkuList) {
        return new BizArkEvent().setEventType(ThirdWarehouseEventEnum.QueryStock).setQueryStock(new QueryStock(inSupplierId, inErpSkuList));
    }

    public static BizArkEvent createOrder(CreateOrder createOrder) {
        return new BizArkEvent().setEventType(ThirdWarehouseEventEnum.CreateOrder).setCreateOrder(createOrder);
    }

    @Setter
    @Getter
    public static class QueryStock {

        /**
         * 入参-供货商租户编号
         */
        private String inSupplierId;

        /**
         * 入参-系统SKU数组
         */
        private List<String> inErpSkuList;

        /**
         * 出参
         */
        private List<OutStock> out;

        protected QueryStock(String inSupplierId, List<String> inErpSkuList) {
            this.inSupplierId = inSupplierId;
            this.inErpSkuList = inErpSkuList;
        }
    }

    @Setter
    @Getter
    public static class CreateOrder {

        /**
         * 入参-供货商租户编号
         */
        private String inSupplierId;

        private String orderNo;
        private String poNumber;
        private String channelAccount;
        private String carrierCode;
        private String warehouseCode;
        private String customerEmail;
        private String thirdPartyAccount;

        private String shipToCountry;
        private String shipToState;
        private String shipToCity;
        private String shipToPostal;
        private String shipToAddress1;
        private String shipToAddress2;
        private String shipToContact;
        private String shipToTelephone;
        private String shipToMobile;

        private String erpSku;
        private Integer quantity;
        private Integer isInsure;
        private Integer isSignature;

        /**
         * 出参
         */
        private LocaleMessage out;

    }

}
