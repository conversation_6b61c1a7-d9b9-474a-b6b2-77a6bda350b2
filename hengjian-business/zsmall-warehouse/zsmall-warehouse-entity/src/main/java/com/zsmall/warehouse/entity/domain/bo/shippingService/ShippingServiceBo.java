package com.zsmall.warehouse.entity.domain.bo.shippingService;

import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.warehouse.entity.domain.ShippingService;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ShippingService.class, reverseConvertGenerate = false)
public class ShippingServiceBo extends BaseEntity {

    /**
     * 物流模板id
     */
    private Long id;
    /**
     * 快递服务编码
     */
    private String code;
    /**
     * 快递服务名称
     */
    private String name;
    /**
     * 物流商唯一代号
     */
    private String logisticsSlug;
    /**
     * 国家二位代号
     */
    private String countryCode;

}
