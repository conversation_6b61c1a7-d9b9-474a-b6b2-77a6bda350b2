package com.zsmall.warehouse.entity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zsmall.common.enums.EffectiveStateType;
import com.zsmall.warehouse.entity.domain.LogisticsConditionEntity;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【logistics_condition(物流子模块表)】的数据库操作Service
* @createDate 2023-05-06 12:02:03
*/
public interface LogisticsConditionService extends IService<LogisticsConditionEntity> {


    /**
     * 根据id和状态查询物流子模板列表
     * @param id
     * @param effectiveStateType
     * @return
     */
    List<LogisticsConditionEntity> findByLogisticsIdAndStatusTypeOrderByCreateDateTimeAsc(Long id,
                                                                                    EffectiveStateType effectiveStateType);

    /**
     * 判定fulfiller名称是否存在
     * @param fulfiller
     * @return
     */
    Boolean existsByFulfillerNameAndLogistics(String fulfiller, Long id);

}
