package com.zsmall.warehouse.entity.domain.vo.logtisticsTemplate;

import lombok.Data;

import java.util.List;

/**
 * 响应体：物流模板详情
 **/
@Data
public class LogisticsTemplateDetailVo {

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 服务编码
     */
    private String serviceCode;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 服务别名，只有Others才有别名
     */
    private String serviceAlias;

    /**
     * 国家二位编号
     */
    private String countryCode;

    /**
     * 服务收费详情
     */
    private List<LogisticsTemplateItemDetailVo> itemDetailList;

    /**
     * 仓库详情
     */
    private List<LogisticsTemplateWarehouseVo> WarehouseList;

}
