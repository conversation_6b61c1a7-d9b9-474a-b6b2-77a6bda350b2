package com.zsmall.warehouse.entity.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.common.enums.EffectiveStateType;
import com.zsmall.warehouse.entity.domain.LogisticsEntity;
import com.zsmall.warehouse.entity.mapper.LogisticsMapper;
import com.zsmall.warehouse.entity.service.ILogisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【logistics(物流表)】的数据库操作Service实现
* @createDate 2023-05-06 12:02:03
*/
@Slf4j
@Service
public class ILogisticsServiceImpl extends ServiceImpl<LogisticsMapper, LogisticsEntity>
    implements ILogisticsService {


    @Override
    public List<LogisticsEntity> findByStatusTypeAndStoreInOrderByCreateDateTimeAsc(EffectiveStateType effectiveStateType, List<Long> storeIds) {
        log.info("进入【查询平台设置的有效的物流模板集合】方法");
        return lambdaQuery().eq(LogisticsEntity::getStatusType, effectiveStateType)
            .in(LogisticsEntity::getStoreId, storeIds)
            .orderByAsc(LogisticsEntity::getCreateTime)
            .list();
    }

    @Override
    public LogisticsEntity findByIdAndStatusType(Long id, EffectiveStateType enable) {
        log.info("进入【查询平台设置的有效的物流模板集合】方法");
        return lambdaQuery().eq(LogisticsEntity::getId, id)
            .eq(LogisticsEntity::getStatusType, enable)
            .one();
    }

    @Override
    public List<LogisticsEntity> findByStoreAndStatusType(Long storeId, EffectiveStateType enable) {
        log.info("进入【查询平台设置的有效的物流模板集合】方法");
        return lambdaQuery().eq(LogisticsEntity::getStoreId, storeId)
            .eq(LogisticsEntity::getStatusType, enable)
            .list();
    }

    @Override
    public List<LogisticsEntity> getLogisticsEntityList() {
        log.info("进入【查询平台设置的有效的物流模板集合】方法");
        return baseMapper.getLogisticsEntityList();
    }

    @Override
    public LogisticsEntity findByIdAndStoreAndStatusType(Long id, Long storeId, EffectiveStateType stateType) {
        log.info("进入【查询平台设置的有效的物流模板集合】方法");
        return lambdaQuery().eq(LogisticsEntity::getId, id)
            .eq(LogisticsEntity::getStoreId, storeId)
            .eq(LogisticsEntity::getStatusType, stateType)
            .one();
    }

    @Override
    public List<LogisticsEntity> findByStatusType(EffectiveStateType stateType) {
        log.info("进入【查询平台设置的有效的物流模板集合】方法");
        return lambdaQuery().eq(LogisticsEntity::getStatusType, stateType).list();
    }
}




