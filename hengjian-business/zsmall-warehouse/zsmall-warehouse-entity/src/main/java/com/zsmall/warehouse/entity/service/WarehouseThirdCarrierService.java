package com.zsmall.warehouse.entity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zsmall.warehouse.entity.domain.WarehouseThirdCarrierEntity;
import com.zsmall.warehouse.entity.domain.enums.CarrierTypeEnum;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【warehouse_third_carrier(仓库第三方物流商)】的数据库操作Service
* @createDate 2022-07-15 15:57:11
*/
public interface WarehouseThirdCarrierService extends IService<WarehouseThirdCarrierEntity> {

  /**
   * 根据仓库id获取第三方物流商信息
   * @param warehouseId
   * @return
   */
  List<WarehouseThirdCarrierEntity> getByWarehouseId(Long warehouseId);

  /**
   * 根据仓库id和第三方物流商，获取第三方物流商信息
   * @param warehouseId
   * @return
   */
  WarehouseThirdCarrierEntity getByWarehouseIdAndType(Long warehouseId, CarrierTypeEnum carrierTypeEnum);

}
