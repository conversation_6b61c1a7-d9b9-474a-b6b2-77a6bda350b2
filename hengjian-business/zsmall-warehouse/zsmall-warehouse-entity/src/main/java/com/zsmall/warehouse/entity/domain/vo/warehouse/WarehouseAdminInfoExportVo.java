package com.zsmall.warehouse.entity.domain.vo.warehouse;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 管理端仓库信息视图对象 warehouse_admin_info
 *
 * <AUTHOR> Li
 * @date 2024-07-09
 */
@Data
public class WarehouseAdminInfoExportVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库唯一系统编号
     */
    @ExcelProperty(value = "仓库唯一系统编号")
    private String warehouseSystemCode;

    /**
     * 仓库编号
     */
    @ExcelProperty(value = "仓库编号")
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @ExcelProperty(value = "仓库名称")
    private String warehouseName;

    /**
     * 仓库类型
     */
    @ExcelProperty(value = "仓库类型")
    private String warehouseType;

    @ExcelIgnore
    private String warehouseTypeName;

    /**
     * 仓库状态（0-停用，1-启用等）
     */
    @ExcelProperty(value = "仓库状态")
    private String warehouseState;

    /**
     * 国家名文本
     */
    @ExcelProperty(value = "国家/地区")
    private String country;

    /**
     * 州/省名文本
     */
    @ExcelProperty(value = "州/省/地区")
    private String state;

    /**
     * 市县名文本
     */
    @ExcelProperty(value = "城市")
    private String city;

    /**
     * 详细地址1
     */
    @ExcelProperty(value = "街道地址")
    private String address1;

    /**
     * 详细地址2
     */
    @ExcelProperty(value = "详细地址")
    private String address2;

    /**
     * 仓库邮编
     */
    @ExcelProperty(value = "邮编")
    private String zipCode;

    /**
     * 仓库管理者姓名
     */
    @ExcelProperty(value = "仓管姓名")
    private String managerName;

    /**
     * 仓库管理者联系电话
     */
    @ExcelProperty(value = "仓管电话")
    private String managerPhone;

    /**
     * 配送国家
     */
    @ExcelProperty(value = "支持配送国家")
    private String distributionCountryName;
    @ExcelIgnore
    private Long id;
}
