package com.zsmall.warehouse.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 物流模板表
 * @TableName logistics_template
 */
@TableName(value ="logistics_template")
@Data
@EqualsAndHashCode(callSuper=false)
public class LogisticsTemplate extends NoDeptTenantEntity {

    private static final long serialVersionUID = 1L;


    /**
     * 自增id主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 国家
     */
    private String countryCode;

    /**
     * 服务编码
     */
    private String serviceCode;

    /**
     * 服务别名
     */
    private String serviceAlias;

    /**
     * 物流模板编号
     */
    private String logisticsTemplateNo;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
