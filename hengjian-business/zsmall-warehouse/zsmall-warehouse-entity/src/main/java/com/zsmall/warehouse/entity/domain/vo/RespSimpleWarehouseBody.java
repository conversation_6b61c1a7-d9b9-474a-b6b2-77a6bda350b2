package com.zsmall.warehouse.entity.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 响应信息-简略仓库信息
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(name = "响应信息-简略仓库信息")
public class RespSimpleWarehouseBody {

  @Schema(title = "仓库Code")
  private String warehouseCode;

  @Schema(title = "仓库名称")
  private String warehouseName;

  @Schema(title = "是否禁用")
  private Boolean disabled;

}
