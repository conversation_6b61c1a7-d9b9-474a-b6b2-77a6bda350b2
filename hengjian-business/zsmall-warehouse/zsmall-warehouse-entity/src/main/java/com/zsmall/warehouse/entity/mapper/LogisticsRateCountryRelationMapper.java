package com.zsmall.warehouse.entity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zsmall.common.domain.SqlLocaleField;
import com.zsmall.warehouse.entity.domain.LogisticsRateCountryRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【logistics_rate_country_relation(物流模板-物流模板费率规则-城市-关联表)】的数据库操作Mapper
* @createDate 2023-05-06 12:02:03
* @Entity generator.domain.LogisticsRateCountryRelation
*/
public interface LogisticsRateCountryRelationMapper extends BaseMapper<LogisticsRateCountryRelation> {

    List<SqlLocaleField> queryLocationName(@Param("templateId") Long templateId);

}




