package com.zsmall.warehouse.entity.domain.bo.warehouse;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.zsmall.warehouse.entity.domain.WarehouseAddress;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 仓库地址信息业务对象 warehouse_address
 *
 * <AUTHOR>
 * @date 2023-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WarehouseAddress.class, reverseConvertGenerate = false)
public class WarehouseAddressBo extends NoDeptBaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 仓库主表主键
     */
    @NotNull(message = "仓库主表主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long warehouseId;

    /**
     * 仓库唯一系统编号
     */
    @NotBlank(message = "仓库唯一系统编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String warehouseSystemCode;

    /**
     * 地区表主键（国家）
     */
    @NotNull(message = "地区表主键（国家）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long countryId;

    /**
     * 国家名文本
     */
    @NotBlank(message = "国家不能为空", groups = { AddGroup.class, EditGroup.class })
    private String country;

    /**
     * 地区表主键（州/省）
     */
    @NotNull(message = "地区表主键（州/省）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long stateId;

    /**
     * 州/省名
     */
    @NotBlank(message = "州/省不能为空", groups = { AddGroup.class, EditGroup.class })
    private String state;

    /**
     * 地区表主键（市县）
     */
    @NotNull(message = "地区表主键（市县）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long cityId;

    /**
     * 市县名文本
     */
    @NotBlank(message = "市县名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String city;

    /**
     * 详细地址1
     */
    @NotBlank(message = "详细地址1不能为空", groups = { AddGroup.class, EditGroup.class })
    private String address1;

    /**
     * 详细地址2
     */
    @NotBlank(message = "详细地址2不能为空", groups = { AddGroup.class, EditGroup.class })
    private String address2;

    /**
     * 仓库邮编
     */
    @NotBlank(message = "仓库邮编不能为空", groups = { AddGroup.class, EditGroup.class })
    private String zipCode;

    /**
     * 仓库管理者姓名
     */
    @NotBlank(message = "仓库管理者姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String managerName;

    /**
     * 仓库管理者联系电话
     */
    @NotBlank(message = "仓库管理者联系电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String managerPhone;

    /**
     * 经度
     */
    @NotNull(message = "经度不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long longitude;

    /**
     * 纬度
     */
    @NotNull(message = "纬度不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long latitude;


}
