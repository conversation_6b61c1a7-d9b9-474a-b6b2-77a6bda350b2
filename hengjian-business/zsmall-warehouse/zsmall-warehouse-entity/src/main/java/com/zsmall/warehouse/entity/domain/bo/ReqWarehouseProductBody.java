package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022-08-27
 **/
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
@Schema(name = "请求参数-仓库查询marketplace参数")
public class ReqWarehouseProductBody {

    @Schema(title = "itemNo")
    private String itemNo;
}
