package com.zsmall.warehouse.entity.iservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.warehouse.entity.domain.LogisticsTemplate;
import com.zsmall.warehouse.entity.mapper.LogisticsTemplateMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据库层：物流模板表
 */
@Service
@Slf4j
public class ILogisticsTemplateService extends ServiceImpl<LogisticsTemplateMapper, LogisticsTemplate> {

    public Boolean existsLogisticsTemplateCode(String logisticsTemplateCode) {
        return baseMapper.existsLogisticsTemplateCode(logisticsTemplateCode);
    }

    /**
     * 是否存在重复的模板名称
     *
     * @param templateName
     * @return
     */
    public Boolean existsTemplateName(String templateName, Long excludeId) {
        LambdaQueryWrapper<LogisticsTemplate> lqw = new LambdaQueryWrapper<>();
        lqw.eq(LogisticsTemplate::getTemplateName, templateName);
        lqw.ne(excludeId != null, LogisticsTemplate::getId, excludeId);
        return baseMapper.exists(lqw);
    }

    public LogisticsTemplate getByLogisticsTemplateNo(String logisticsTemplateNo) {
        log.info("进入【物流模板信息查询】, logisticsTemplateNo = {}", logisticsTemplateNo);
        LambdaQueryWrapper<LogisticsTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LogisticsTemplate::getLogisticsTemplateNo, logisticsTemplateNo);
        return this.getOne(queryWrapper);
    }

    @InMethodLog("根据仓库查询所有关联的物流模板")
    public List<LogisticsTemplate> queryByWarehouse(String warehouseSystemCode) {
        return baseMapper.queryByWarehouse(warehouseSystemCode);
    }

    @InMethodLog("根据仓库和模板名查询关联的物流模板")
    public LogisticsTemplate queryByWarehouse(String warehouseSystemCode, String templateName) {
        return baseMapper.queryByWarehouseAndTemplateName(warehouseSystemCode, templateName);
    }

    @InMethodLog("根据提供的参数查询充足库存关联的物流模板")
    public List<LogisticsTemplate> queryAdequateStockLogisticsTemplate(String productSkuCode, Integer adjustQuantity, Boolean logisticsAccount) {
        return baseMapper.queryAdequateStockLogisticsTemplate(productSkuCode, adjustQuantity, logisticsAccount);
    }

    /**
     * 批量查询仓库关联的物流模板（使用IN查询）
     * @param warehouseSystemCodes 仓库系统编码集合
     * @return 物流模板Map，key为仓库编码，value为物流模板列表
     */
    @InMethodLog("批量查询仓库关联的物流模板")
    public Map<String, List<LogisticsTemplate>> queryByWarehousesMap(List<String> warehouseSystemCodes) {
        if (CollUtil.isEmpty(warehouseSystemCodes)) {
            return new HashMap<>();
        }

        // 使用 IN 查询一次性获取所有仓库的物流模板（包含仓库编码）
        List<Map<String, Object>> rawResults = baseMapper.queryByWarehouses(CollUtil.newHashSet(warehouseSystemCodes));
        // 转换结果并按仓库编码分组
        Map<String, List<LogisticsTemplate>> result = new HashMap<>();
        for (Map<String, Object> rawResult : rawResults) {
            String warehouseCode = (String) rawResult.get("warehouseSystemCode");
            LogisticsTemplate template = BeanUtil.toBean(rawResult, LogisticsTemplate.class);
            result.computeIfAbsent(warehouseCode, k -> new ArrayList<>()).add(template);
        }

        log.info("【批量查询物流模板】使用IN查询，仓库数量: {}, 查询结果: {}条",
                warehouseSystemCodes.size(), rawResults.size());
        return result;
    }


}




