package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/19
 **/
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "新增-编辑物流模板服务费用详情参数")
public class ReqLogisticsTemplateItemFormBody {

    @Schema(title = "模板详情id，编辑时传入")
    private Long templateItemId;

    @Schema(title = "费率类型")
    private Integer rateType;

    @Schema(title = "基础标准")
    private BigDecimal within;

    @Schema(title = "每一个额外标准")
    private BigDecimal everyAdditionalWithin;

    @Schema(title = "基础费用")
    private BigDecimal withinFee;

    @Schema(title = "每一个额外标准的费用")
    private BigDecimal everyAdditionalFee;

    @Schema(title = "货物单位")
    private String unit;

    @Schema(title = "最快送达时间")
    private Integer fastestDeliveryTime;

    @Schema(title = "最慢送达时间")
    private Integer slowestDeliveryTime;

    @Schema(title = "目的地城市列表")
    private List<ReqLogisticsTemplateShipToFormBody> shipToBodyList;

}
