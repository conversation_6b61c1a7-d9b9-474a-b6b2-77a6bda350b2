package com.zsmall.warehouse.entity.iservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.warehouse.WarehouseTypeEnum;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.domain.WarehouseAddress;
import com.zsmall.warehouse.entity.domain.WarehouseAdminInfo;
import com.zsmall.warehouse.entity.domain.bo.warehouse.WarehouseBo;
import com.zsmall.warehouse.entity.domain.vo.warehouse.WarehouseSelectVo;
import com.zsmall.warehouse.entity.domain.vo.warehouse.WarehouseVo;
import com.zsmall.warehouse.entity.mapper.WarehouseAddressMapper;
import com.zsmall.warehouse.entity.mapper.WarehouseAdminInfoMapper;
import com.zsmall.warehouse.entity.mapper.WarehouseMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 仓库管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-22
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IWarehouseService extends ServiceImpl<WarehouseMapper, Warehouse> {

    private final WarehouseMapper baseMapper;
    private final WarehouseAdminInfoMapper warehouseAdminInfoMapper;
    private final WarehouseAddressMapper warehouseAddressMapper;

    public Warehouse getByIdNotTenant(Long warehouseId) {
        return TenantHelper.ignore(() -> baseMapper.selectById(warehouseId));
    }

    public boolean existsWarehouseSystemCode(String warehouseSystemCode) {
        return baseMapper.existsWarehouseSystemCode(warehouseSystemCode);
    }

    @InMethodLog("查询当前供货商是否存在重复的仓库编号")
    public boolean existsWarehouseCode(String tenantId, String warehouseCode, String warehouseType, Long excludeId) {
        LambdaQueryWrapper<Warehouse> lqw = Wrappers.lambdaQuery();
        lqw.eq(Warehouse::getTenantId, tenantId);
        lqw.eq(Warehouse::getWarehouseType, warehouseType);
        lqw.eq(Warehouse::getWarehouseCode, warehouseCode);
        lqw.ne(excludeId != null, Warehouse::getId, excludeId);
        return TenantHelper.ignore(() -> baseMapper.exists(lqw));
    }

    @InMethodLog("查询当前供货商是否存在重复的仓库编号")
    public boolean existsWarehouseName(String tenantId, String warehouseCode, String warehouseName, Long excludeId) {
        LambdaQueryWrapper<Warehouse> lqw = Wrappers.lambdaQuery();
        lqw.eq(Warehouse::getTenantId, tenantId);
//        lqw.eq(Warehouse::getWarehouseType, warehouseType);
        lqw.eq(Warehouse::getWarehouseCode, warehouseCode);
        lqw.eq(Warehouse::getWarehouseName, warehouseName);
        lqw.ne(excludeId != null, Warehouse::getId, excludeId);
        return TenantHelper.ignore(() -> baseMapper.exists(lqw));
    }

    /**
     * 根据仓库唯一系统编号数组批量查询（无视租户）
     * @param warehouseSystemCodeList
     * @return
     */
    public List<Warehouse> queryByWarehouseSystemCodeListNotTenant(Collection<?> warehouseSystemCodeList) {
        LambdaQueryWrapper<Warehouse> lqw = Wrappers.lambdaQuery();
        lqw.in(Warehouse::getWarehouseSystemCode, warehouseSystemCodeList);
        lqw.eq(Warehouse::getWarehouseState, GlobalStateEnum.Valid.getCode());
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
    }

    /**
     * 根据仓库唯一系统编号查询（无视租户）
     * @param warehouseSystemCode
     * @return
     */
    public Warehouse queryByWarehouseSystemCodeNotTenant(String warehouseSystemCode) {
        LambdaQueryWrapper<Warehouse> lqw = Wrappers.lambdaQuery();
        lqw.eq(Warehouse::getWarehouseSystemCode, warehouseSystemCode);
        lqw.eq(Warehouse::getWarehouseState, GlobalStateEnum.Valid.getCode());
        return baseMapper.selectOneNotTenant(lqw);
    }

    /**
     * 根据仓库唯一系统编号查询（有租户）
     * @param tenantId
     * @param warehouseSystemCode
     * @return
     */
    public Warehouse queryByWarehouseSystemCodeHasTenant(String tenantId, String warehouseSystemCode) {
        LambdaQueryWrapper<Warehouse> lqw = Wrappers.lambdaQuery();
        lqw.eq(Warehouse::getTenantId, tenantId);
        lqw.eq(Warehouse::getWarehouseSystemCode, warehouseSystemCode);
        lqw.eq(Warehouse::getWarehouseState, GlobalStateEnum.Valid.getCode());
        return baseMapper.selectOneNotTenant(lqw);
    }

    /**
     * 根据仓库唯一系统编号查询
     * @param warehouseSystemCode
     * @return
     */
    public Warehouse queryByWarehouseSystemCode(String warehouseSystemCode) {
        LambdaQueryWrapper<Warehouse> lqw = Wrappers.lambdaQuery();
        lqw.eq(Warehouse::getWarehouseSystemCode, warehouseSystemCode);
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw), TenantType.Manager, TenantType.Distributor);
    }

    /**
     * 查询仓库管理
     */
    public WarehouseVo selectVoById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询仓库管理列表
     */
    public TableDataInfo<WarehouseVo> queryPageList(WarehouseBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Warehouse> lqw = buildQueryWrapper(bo);
        Page<WarehouseVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询仓库管理列表
     */
    public List<WarehouseVo> queryList(WarehouseBo bo) {
        LambdaQueryWrapper<Warehouse> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询仓库管理列表（提供给下拉选使用）
     *
     * @param bo
     */
    public List<WarehouseSelectVo> queryListForSelect(WarehouseBo bo) throws RStatusCodeException {
        String warehouseType = bo.getWarehouseType();
        if (StrUtil.isNotBlank(warehouseType)) {
            LambdaQueryWrapper<Warehouse> lqw = Wrappers.lambdaQuery();
            lqw.eq(Warehouse::getWarehouseType, WarehouseTypeEnum.valueOf(warehouseType));
            lqw.eq(Warehouse::getWarehouseState, GlobalStateEnum.Valid);
            List<Warehouse> warehouses = baseMapper.selectList(lqw);
            return BeanUtil.copyToList(warehouses, WarehouseSelectVo.class);
        } else {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
    }

    private LambdaQueryWrapper<Warehouse> buildQueryWrapper(WarehouseBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Warehouse> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseType()), Warehouse::getWarehouseType, bo.getWarehouseType());
        lqw.like(StringUtils.isNotBlank(bo.getWarehouseName()), Warehouse::getWarehouseName, bo.getWarehouseName());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseCode()), Warehouse::getWarehouseCode, bo.getWarehouseCode());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseSystemCode()), Warehouse::getWarehouseSystemCode, bo.getWarehouseSystemCode());
        lqw.eq(bo.getWarehouseState() != null, Warehouse::getWarehouseState, bo.getWarehouseState());
        lqw.eq(StringUtils.isNotBlank(bo.getZipCode()), Warehouse::getZipCode, bo.getZipCode());
        lqw.orderByDesc(Warehouse::getId);
        // lqw.eq(bo.getIsSupportThirdCarrier() != null, Warehouse::getIsSupportThirdCarrier, bo.getIsSupportThirdCarrier());
        return lqw;
    }

    /**
     * 批量删除仓库管理
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据仓库唯一编号判断是否支持第三方物流
     * @param warehouseSystemCode
     * @return
     */
    public boolean isSupportThirdCarrier(String warehouseSystemCode) {
        return baseMapper.isSupportThirdCarrier(warehouseSystemCode);
    }

    @InMethodLog("根据仓库类型查询仓库")
    public List<Warehouse> queryByWarehouseType(WarehouseTypeEnum warehouseType) {
        return lambdaQuery().eq(Warehouse::getWarehouseType, warehouseType).list();
    }

    @InMethodLog("根据租户、仓库类型、仓库编号查询仓库")
    public Warehouse queryByWarehouseType(String tenantId, WarehouseTypeEnum warehouseType, String warehouseCode) {
        return lambdaQuery().eq(Warehouse::getTenantId, tenantId).eq(Warehouse::getWarehouseType, warehouseType).eq(Warehouse::getWarehouseCode, warehouseCode).one();
    }

    /**
     *  根据SKU唯一编号 查询所属的仓库信息
     * @param productSkuCode 商品SKU唯一编号
     * @return
     */
    @InMethodLog("根据SKU唯一编号查询所属的仓库信息")
    public List<Warehouse> getWarehouseByProductSkuCode(String productSkuCode) {
        return baseMapper.getWarehouseByProductSkuCode(productSkuCode);
    }

    @InMethodLog("更新管理员仓库信息联动修改供应商仓库信息")
    @Transactional(rollbackFor = Exception.class)
    public void updateWarehouseInfoByAdmin(String warehouseSystemCode) {
        if(StringUtils.isEmpty(warehouseSystemCode)){
            log.warn("更新管理员仓库信息联动修改供应商仓库信息的仓库系统编号为空");
            return;
        }
        LambdaQueryWrapper<WarehouseAdminInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WarehouseAdminInfo::getWarehouseSystemCode, warehouseSystemCode);
        WarehouseAdminInfo warehouseAdminInfo = warehouseAdminInfoMapper.selectOne(queryWrapper);
        if(null != warehouseAdminInfo){
            // 判断仓库code是否重复
            boolean existsed = existsWarehouseCode(warehouseAdminInfo.getTenantId(), warehouseAdminInfo.getWarehouseCode(), warehouseAdminInfo.getWarehouseType().toString(), null);
            if (existsed) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.THE_SAME_WAREHOUSE_CODE_EXISTS);
            }
            // 判断仓库名称是否重复
            boolean existsedByWarehouseName = existsWarehouseName(warehouseAdminInfo.getTenantId(), warehouseAdminInfo.getWarehouseCode(),warehouseAdminInfo.getWarehouseName(), null);
            if (existsedByWarehouseName) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.THE_SAME_WAREHOUSE_CODE_EXISTS);
            }
            LambdaQueryWrapper<Warehouse> warehouseQueryWrapper = new LambdaQueryWrapper<>();
            warehouseQueryWrapper.eq(Warehouse::getWarehouseSystemCode, warehouseAdminInfo.getWarehouseSystemCode());
            // 仓库信息更新
            Warehouse warehouse = baseMapper.selectOne(warehouseQueryWrapper);
            if(null == warehouse){
                return;
            }
            warehouse.setWarehouseName(warehouseAdminInfo.getWarehouseName());
            warehouse.setWarehouseCode(warehouseAdminInfo.getWarehouseCode());
            warehouse.setZipCode(warehouseAdminInfo.getZipCode());
            baseMapper.updateById(warehouse);
            // 仓库地址信息更新
            LambdaQueryWrapper<WarehouseAddress> warehouseAddressQueryWrapper = new LambdaQueryWrapper<>();
            warehouseAddressQueryWrapper.eq(WarehouseAddress::getWarehouseId,warehouse.getId());
            WarehouseAddress warehouseAddress = warehouseAddressMapper.selectOne(warehouseAddressQueryWrapper);
            if(null == warehouseAddress){
                return;
            }
            warehouseAddress.setZipCode(warehouseAdminInfo.getZipCode());
            warehouseAddress.setManagerName(warehouseAdminInfo.getManagerName());
            warehouseAddress.setManagerPhone(warehouseAdminInfo.getManagerPhone());
            warehouseAddressMapper.updateById(warehouseAddress);
        }
    }

    /**
     * 功能描述：按仓库查询
     *
     * @param specifyWarehouse 指定仓库
     * @param tenantId         租户id
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/08/05
     */
    public String queryByWarehouse(String specifyWarehouse, String tenantId) {
        Warehouse warehouse = TenantHelper.ignore(()->baseMapper.selectOne(Wrappers.lambdaQuery(Warehouse.class)
                                                                                   .eq(Warehouse::getWarehouseCode, specifyWarehouse)
                                                                                   .eq(Warehouse::getTenantId, tenantId)));


        return warehouse.getWarehouseSystemCode();
    }

    /**
     * 功能描述：按租户、仓库编号、仓库类型查询仓库
     * @param tenantId
     * @param warehouseCode
     * @param warehouseType
     * @return
     */
    public List<Warehouse> queryListByTenantIdAndWarehouseCodeAndWarehouseType(String tenantId, String warehouseCode, WarehouseTypeEnum warehouseType) {
        return lambdaQuery().eq(Warehouse::getTenantId, tenantId).eq(Warehouse::getWarehouseCode, warehouseCode).eq(Warehouse::getWarehouseType, warehouseType).eq(Warehouse::getWarehouseState, 1).list();
    }

    public Warehouse queryByTenantIdAndWarehouseCodeAndWarehouseType(String tenantId, String warehouseCode, WarehouseTypeEnum warehouseType) {
        return TenantHelper.ignore(()->lambdaQuery().eq(Warehouse::getTenantId, tenantId).eq(Warehouse::getWarehouseCode, warehouseCode).eq(Warehouse::getWarehouseType, warehouseType).eq(Warehouse::getWarehouseState, 1).one());
    }

    /**
     * @description: 根据仓库编码查询仓库地址信息
     * @author: Len
     * @date: 2024/10/24 17:47
     * @param: warehouseSystemCode
     * @return: java.util.List<java.util.HashMap<java.lang.String,java.lang.Object>>
     **/

    public String getWarehouseAddressInfo(String warehouseSystemCode) {
        return TenantHelper.ignore(()->baseMapper.getWarehouseAddressInfo(warehouseSystemCode)) ;
    }

    /**
     * 根据仓库系统编码获取仓库可配送国家信息
     * @param warehouseSystemCode
     * @return
     */
    public List<String> listCountryByWarehouseSystemCode(String warehouseSystemCode) {
        return baseMapper.listCountryByWarehouseSystemCode(warehouseSystemCode);
    }
}
