package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 请求参数-切换库存销售状态
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "请求参数-切换库存销售状态")
public class ReqSwitchStockStatusBody {

    @Schema(title = "库存编号")
    private String inventoryCode;

    @Schema(title = "状态：Enable-在售，Disable-停售")
    private String status;

}
