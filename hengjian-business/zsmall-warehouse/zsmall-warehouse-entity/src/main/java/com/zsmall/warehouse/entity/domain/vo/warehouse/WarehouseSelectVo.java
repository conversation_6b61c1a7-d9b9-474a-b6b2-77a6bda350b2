package com.zsmall.warehouse.entity.domain.vo.warehouse;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.warehouse.entity.domain.Warehouse;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 仓库管理视图对象 warehouse（下拉选使用）
 *
 * <AUTHOR>
 * @date 2023-05-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Warehouse.class)
public class WarehouseSelectVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库类型
     */
    @ExcelProperty(value = "仓库类型")
    private String warehouseType;

    /**
     * 仓库名称
     */
    @ExcelProperty(value = "仓库名称")
    private String warehouseName;

    /**
     * 仓库唯一系统编号
     */
    @ExcelProperty(value = "仓库唯一系统编号")
    private String warehouseSystemCode;

    /**
     * 可选物流模板
     */
    @ExcelProperty(value = "可选物流模板")
    private List<LogisticsTemplateSelectVo> logisticsTemplateSelect = new ArrayList<>();

    @Getter
    @Setter
    public class LogisticsTemplateSelectVo {

        /**
         * 模板名称
         */
        private String templateName;

        /**
         * 物流模板编号
         */
        private String logisticsTemplateNo;

    }
}
