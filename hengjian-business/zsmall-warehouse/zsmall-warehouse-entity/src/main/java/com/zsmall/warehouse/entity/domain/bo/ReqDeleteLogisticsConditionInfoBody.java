package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 请求参数-删除物流条件信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "请求参数-删除物流条件信息")
public class ReqDeleteLogisticsConditionInfoBody {

    @Schema(title = "物流条件信息ids")
    private List<Long> infoIds;

    @Schema(title = "是否员工账号")
    private Boolean isDepot;
}
