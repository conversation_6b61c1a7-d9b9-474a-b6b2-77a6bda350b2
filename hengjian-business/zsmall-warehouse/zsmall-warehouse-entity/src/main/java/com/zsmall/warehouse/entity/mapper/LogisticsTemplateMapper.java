package com.zsmall.warehouse.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zsmall.warehouse.entity.domain.LogisticsTemplate;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【logistics_template(物流模板表)】的数据库操作Mapper
* @createDate 2023-05-06 12:02:03
* @Entity generator.domain.LogisticsTemplate
*/
public interface LogisticsTemplateMapper extends BaseMapper<LogisticsTemplate> {

    Boolean existsLogisticsTemplateCode(@Param("logisticsTemplateCode") String logisticsTemplateCode);

    List<LogisticsTemplate> queryByWarehouse(@Param("warehouseSystemCode") String warehouseSystemCode);
    List<Map<String, Object>> queryByWarehouses(@Param("warehouseSystemCode") Set<String> warehouseSystemCode);

    LogisticsTemplate queryByWarehouseAndTemplateName(@Param("warehouseSystemCode") String warehouseSystemCode, @Param("templateName") String templateName);

    /**
     * 根据提供的参数查询充足的库存关联物流模板
     */
    @InterceptorIgnore(tenantLine = "true")
    List<LogisticsTemplate> queryAdequateStockLogisticsTemplate(@Param("productSkuCode") String productSkuCode,
                                                     @Param("adjustQuantity") Integer adjustQuantity,
                                                     @Param("logisticsAccount") Boolean logisticsAccount);

}




