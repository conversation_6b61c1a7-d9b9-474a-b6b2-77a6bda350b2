package com.zsmall.warehouse.entity.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 响应信息-履约仓库配置
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(name = "响应信息-履约仓库配置")
public class RespFulfillWarehouseBody {

  @Schema(title = "仓库服务商名称")
  private String warehouseName;

  @Schema(title = "仓库所属商铺Code")
  private String warehouseStoreCode;

  @Schema(title = "仓库Code")
  private String warehouseCode;

  @Schema(title = "仓库key")
  private String keyId;

  @Schema(title = "国家")
  private String country_zh_CN;

  @Schema(title = "国家")
  private String country_en_US;

  @Schema(title = "州/省")
  private String state_zh_CN;

  @Schema(title = "州/省")
  private String state_en_US;

  @Schema(title = "城市")
  private String city_zh_CN;

  @Schema(title = "城市")
  private String city_en_US;

  @Schema(title = "详细地址1")
  private String address1;

  @Schema(title = "详细地址2")
  private String address2;

  @Schema(title = "城市")
  private String city;

}
