package com.zsmall.warehouse.entity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zsmall.warehouse.entity.domain.LogisticsEntity;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【logistics(物流表)】的数据库操作Mapper
* @createDate 2023-05-06 12:02:03
* @Entity generator.domain.Logistics
*/
public interface LogisticsMapper extends BaseMapper<LogisticsEntity> {

    /**
     * 查询平台设置的有效的物流模板集合
     *
     * @return
     */
    List<LogisticsEntity> getLogisticsEntityList();

}




