package com.zsmall.warehouse.entity.iservice;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.UpdateChainWrapper;
import com.baomidou.mybatisplus.extension.kotlin.KtQueryChainWrapper;
import com.baomidou.mybatisplus.extension.kotlin.KtUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.zsmall.warehouse.entity.domain.ShippingService;
import com.zsmall.warehouse.entity.domain.WarehouseAdminDeliveryCountry;
import com.zsmall.warehouse.entity.domain.bo.warehouse.WarehouseAdminDeliveryCountryBo;
import com.zsmall.warehouse.entity.domain.vo.warehouse.WarehouseAdminDeliveryCountryVo;
import com.zsmall.warehouse.entity.mapper.ShippingServiceMapper;
import com.zsmall.warehouse.entity.mapper.WarehouseAdminDeliveryCountryMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 仓库地址支持配送国家Service接口
 *
 * <AUTHOR> Li
 * @date 2024-12-17
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IWarehouseAdminDeliveryCountryService extends ServiceImpl<WarehouseAdminDeliveryCountryMapper, WarehouseAdminDeliveryCountry> {

}
