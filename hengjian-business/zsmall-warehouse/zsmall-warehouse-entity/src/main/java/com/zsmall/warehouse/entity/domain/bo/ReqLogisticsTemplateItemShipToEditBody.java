package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/24
 **/
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "编辑物流模板的目的地参数")
public class ReqLogisticsTemplateItemShipToEditBody {

    @Schema(title = "模板详情id")
    private Long templateItemId;

    @Schema(title = "目的地城市列表")
    private List<ReqLogisticsTemplateShipToFormBody> shipToBodyList;
}
