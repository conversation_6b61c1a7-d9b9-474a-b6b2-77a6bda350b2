package com.zsmall.warehouse.entity.domain.vo.logtisticsTemplate;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/20
 **/
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
@Schema(name = "响应信息-物流模板列表信息")
public class LogisticsTemplateVo {

    /**
     * 模板id
     */
    private Long id;

    /**
     * 物流模板名称
     */
    private String templateName;

    /**
     * 仓库名称
     */
    private List<String> warehouseNameList;

    /**
     * 物流服务商
     */
    private String shippingService;

    /**
     * 国家
     */
    private String countryCode;

    /**
     * 目的地
     */
    private List<String> shipToList_zh_CN;

    /**
     * 目的地
     */
    private List<String> shipToList_en_US;

}
