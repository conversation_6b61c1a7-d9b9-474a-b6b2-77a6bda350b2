package com.zsmall.warehouse.entity.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/1/27
 **/
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
@Schema(name = "响应信息-运费计算信息（在商品详情页进行初步计算）")
public class RespLogisticsFee2ProductBody {

  @Schema(title = "预计运费")
  private String shippingCost;

  @Schema(title = "预计到达时间（英文）")
  private String deliveryTime;

  @Schema(title = "预计到达时间（中文）")
  private String deliveryTimeCN;

  @Schema(title = "城市名（中文）")
  private String stateNameCn;

  @Schema(title = "城市名（英文）")
  private String stateNameEn;
}
