package com.zsmall.warehouse.entity.domain.vo.warehouse;

import com.baomidou.mybatisplus.annotation.TableId;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.warehouse.entity.domain.Warehouse;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024年7月10日  17:24
 * @description:
 */
@Data
@AutoMapper(target = Warehouse.class, reverseConvertGenerate = false)
public class WarehouseAdminVo extends NoDeptTenantEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *  管理员仓库数据主键
     */
    private Long id;

    /**
     * 全部的地址信息
     */
    private String allAddresses;

    /**
     * 仓库类型（区分自有仓库与第三方仓库）
     */
    private String warehouseType;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库编号
     */
    private String warehouseCode;

    /**
     * 仓库唯一系统编号
     */
    private String warehouseSystemCode;

    /**
     * 仓库状态（0-停用，1-启用等）
     */
    private Integer warehouseState;

    /**
     * 是否支持第三方物流账号（0-否，1-是）
     */
    private Boolean supportLogisticsAccount;

    /**
     * 地区表主键（国家）
     */
    private Long countryId;

    /**
     * 国家名文本
     */
    private String country;

    /**
     * 地区表主键（州/省）
     */
    private Long stateId;

    /**
     * 州/省名文本
     */
    private String state;

    /**
     * 地区表主键（市县）
     */
    private Long cityId;

    /**
     * 市县名文本
     */
    private String city;

    /**
     * 详细地址1
     */
    private String address1;

    /**
     * 详细地址2
     */
    private String address2;

    /**
     * 仓库邮编
     */
    private String zipCode;

    /**
     * 仓库管理者姓名
     */
    private String managerName;

    /**
     * 仓库管理者联系电话
     */
    private String managerPhone;

    /**
     * 经度
     */
    private Long longitude;

    /**
     * 纬度
     */
    private Long latitude;
}
