package com.zsmall.warehouse.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 物流模板-仓库关联表
 */
@TableName(value ="logistics_warehouse_relation")
@Data
@EqualsAndHashCode(callSuper=false)
public class LogisticsWarehouseRelation extends NoDeptBaseEntity {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 物流模板id
     */
    private Long logisticsTemplateId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
