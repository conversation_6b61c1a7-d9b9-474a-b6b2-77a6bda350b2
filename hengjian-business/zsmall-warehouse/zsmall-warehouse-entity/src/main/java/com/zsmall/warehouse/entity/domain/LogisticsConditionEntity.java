package com.zsmall.warehouse.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 物流子模块表
 * @TableName logistics_condition
 */
@TableName(value ="logistics_condition")
@Data
@EqualsAndHashCode(callSuper=false)
public class LogisticsConditionEntity extends SortEntity implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 国家
     */
    private String country;

    /**
     * 条件名称
     */
    private String fulfillerName;

    /**
     * 长度单位
     */
    private String lengthUnit;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 快递账号
     */
    private String logisticsCompanyId;

    /**
     * 模板id
     */
    private Long logisticsId;

    /**
     * 有效状态
     */
    private String statusType;

    /**
     * 重量单位
     */
    private String weightUnit;

    /**
     * 主键
     */
    private Long storeWarehouseId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
