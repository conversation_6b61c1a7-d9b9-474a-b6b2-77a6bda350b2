package com.zsmall.warehouse.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.warehouse.entity.domain.LogisticsTemplateRateRule;
import com.zsmall.warehouse.entity.mapper.LogisticsTemplateRateRuleMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据库层：物流模板类型表
 */
@Service
@Slf4j
public class ILogisticsTemplateRateRuleService extends ServiceImpl<LogisticsTemplateRateRuleMapper, LogisticsTemplateRateRule> {

    @InMethodLog("根据id集合查询结果集")
    public List<LogisticsTemplateRateRule> getByIds(List<Long> rateRuleIds) {
        LambdaQueryWrapper<LogisticsTemplateRateRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(LogisticsTemplateRateRule::getId, rateRuleIds);
        return this.list(queryWrapper);
    }

    @InMethodLog("根据商品SKU编号查询所有")
    public List<LogisticsTemplateRateRule> queryByProductSkuCode(String productSkuCode) {
        return baseMapper.queryByProductSkuCode(productSkuCode);
    }

}




