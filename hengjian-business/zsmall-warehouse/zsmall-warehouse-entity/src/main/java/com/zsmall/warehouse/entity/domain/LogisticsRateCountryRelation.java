package com.zsmall.warehouse.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 物流模板-物流模板费率规则-城市-关联表
 * @TableName logistics_rate_country_relation
 */
@TableName(value ="logistics_rate_country_relation")
@Data
@EqualsAndHashCode(callSuper=false)
public class LogisticsRateCountryRelation extends NoDeptBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 物流模板id
     */
    private Long logisticsTemplateId;

    /**
     * 物流模板费率规则id
     */
    private Long logisticsTemplateRateRuleId;

    /**
     * 地点id
     */
    private Long worldLocationId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
