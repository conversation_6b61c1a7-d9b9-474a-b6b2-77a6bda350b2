package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022-08-27
 **/
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
@Schema(name = "请求参数-仓库查询marketplace参数")
public class ReqWarehouseBody {

    @Schema(title = "查询类型（仓库名称：warehouseName, 仓库编码：warehouseID，城市：city，洲：state，邮编：zipCode，itemNo，sku）")
    private String queryType;

    @Schema(title = "查询关键字")
    private String queryValue;
}
