package com.zsmall.warehouse.entity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zsmall.common.enums.EffectiveStateType;
import com.zsmall.warehouse.entity.domain.LogisticsEntity;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【LogisticsEntity(物流表)】的数据库操作Service
* @createDate 2023-05-06 12:02:03
*/
public interface ILogisticsService extends IService<LogisticsEntity> {


    /**
     * 根据状态查询模板列表
     * @param effectiveStateType
     * @param storeIds
     * @return
     */
    List<LogisticsEntity> findByStatusTypeAndStoreInOrderByCreateDateTimeAsc(EffectiveStateType effectiveStateType,
                                                                             List<Long> storeIds);

    /**
     * 根据id和状态查询物流
     * @param id
     * @param enable
     * @return
     */
    LogisticsEntity findByIdAndStatusType(Long id, EffectiveStateType enable);

    /**
     * 根据商店和状态查询物流集合
     * @param storeId
     * @param enable
     * @return
     */
    List<LogisticsEntity> findByStoreAndStatusType(Long storeId, EffectiveStateType enable);

    /**
     * 查询平台设置的有效的物流模板集合
     *
     * @return
     */
    List<LogisticsEntity> getLogisticsEntityList();

    /**
     * 根据物流id、商户、状态查询物流
     * @param id
     * @param storeId
     * @param stateType
     * @return
     */
    LogisticsEntity findByIdAndStoreAndStatusType(Long id, Long storeId, EffectiveStateType stateType);

    /**
     * 根据有效状态查询物流
     * @param stateType
     * @return
     */
    List<LogisticsEntity> findByStatusType(EffectiveStateType stateType);
}
