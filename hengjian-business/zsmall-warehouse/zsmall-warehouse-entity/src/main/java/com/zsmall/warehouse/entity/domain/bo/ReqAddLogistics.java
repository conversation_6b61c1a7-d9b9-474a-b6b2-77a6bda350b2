package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 添加物流模板
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "请求参数-添加物流模板")
public class ReqAddLogistics {

    @Schema(title = "模板名称")
    private String logisticsName;

    @Schema(title = "模板id")
    private Long id;

    @Schema(title = "是否员工账号")
    private Boolean isDepot;

    @Schema(title = "国家id")
    private Long country;
}
