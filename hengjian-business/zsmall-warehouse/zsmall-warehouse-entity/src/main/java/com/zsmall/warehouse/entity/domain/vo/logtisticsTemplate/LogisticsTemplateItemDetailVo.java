package com.zsmall.warehouse.entity.domain.vo.logtisticsTemplate;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/25
 **/
@Data
public class LogisticsTemplateItemDetailVo {

    /**
     * 模板详情id
     */
    @Schema(title = "模板详情id")
    private Long templateItemId;

    /**
     * 最快送达时间
     */
    private Integer fastestDeliveryTime;

    /**
     * 最慢送达时间
     */
    private Integer slowestDeliveryTime;

    @Schema(title = "目的地城市列表")
    private List<LogisticsTemplateShipToDetailVo> shipToBodyList;
}
