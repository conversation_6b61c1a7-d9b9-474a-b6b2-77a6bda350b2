package com.zsmall.warehouse.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.warehouse.entity.domain.WarehouseAddress;
import com.zsmall.warehouse.entity.domain.vo.warehouse.WarehouseAddressVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 仓库地址信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-24
 */
public interface WarehouseAddressMapper extends BaseMapperPlus<WarehouseAddress, WarehouseAddressVo> {

    @InterceptorIgnore(tenantLine = "true")
    List<WarehouseAddress> queryByProductSkuCode(@Param("productSkuCode") String productSkuCode);

    @InterceptorIgnore(tenantLine = "true")
    IPage<WarehouseAddress> queryByProductMappingChannel(Page<WarehouseAddress> queryPage, @Param("channelId") Long channelId, @Param("queryValue") String queryValue);

}
