package com.zsmall.warehouse.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 第三方物流信息表
 * @TableName third_party_logistics
 */
@TableName(value ="third_party_logistics")
@Data
@EqualsAndHashCode(callSuper=false)
public class ThirdPartyLogisticsEntity extends SortEntity implements Serializable {
    /**
     * 主键（物流商唯一代号）
     */
    @TableId
    private String slug;

    /**
     * 默认语言
     */
    private String defaultLanguage;

    /**
     * 物流商名称
     */
    private String name;

    /**
     * 物流商完整名称
     */
    private String otherName;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 有效状态
     */
    private String statusType;

    /**
     * 官方网站
     */
    private String webUrl;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
