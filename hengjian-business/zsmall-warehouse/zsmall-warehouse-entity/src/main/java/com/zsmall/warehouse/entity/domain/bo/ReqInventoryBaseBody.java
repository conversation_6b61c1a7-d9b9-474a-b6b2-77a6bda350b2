package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 请求参数-库存基础信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "请求参数-库存基础信息")
public class ReqInventoryBaseBody {

    @Schema(title = "库存编号")
    private String stockCode;

    @Schema(title = "仓库编号")
    private String warehouseSystemCode;

    @Schema(title = "数量")
    private Integer quantity;

}
