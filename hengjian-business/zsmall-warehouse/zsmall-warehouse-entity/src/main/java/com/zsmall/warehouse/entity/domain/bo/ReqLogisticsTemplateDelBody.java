package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/1/19
 **/
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "删除物流模板参数")
public class ReqLogisticsTemplateDelBody {

    @Schema(title = "物流模板id")
    private Long id;

}
