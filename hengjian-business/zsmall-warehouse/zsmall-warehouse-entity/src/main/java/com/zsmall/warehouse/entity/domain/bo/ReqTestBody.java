package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/3/2
 **/
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "请求参数-测试参数1")
public class ReqTestBody {

    @Schema(title = "arg1")
    private String arg1;
    @Schema(title = "arg2")
    private String arg2;
    @Schema(title = "arg3")
    private String arg3;
    @Schema(title = "arg4")
    private String arg4;
    @Schema(title = "arg5")
    private String arg5;


}
