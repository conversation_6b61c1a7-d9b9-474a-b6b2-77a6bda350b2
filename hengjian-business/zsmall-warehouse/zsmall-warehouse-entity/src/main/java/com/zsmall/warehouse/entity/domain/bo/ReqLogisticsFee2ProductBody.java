package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/2/11
 **/
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
@Schema(name = "请求信息-运费计算信息（在商品详情页进行初步计算）")
public class ReqLogisticsFee2ProductBody {

    @Schema(title = "商品Code")
    private String productCode;

    @Schema(title = "商品skuCode")
    private String skuCode;

    @Schema(title = "目的地地区编码")
    private String shipToCode;
}
