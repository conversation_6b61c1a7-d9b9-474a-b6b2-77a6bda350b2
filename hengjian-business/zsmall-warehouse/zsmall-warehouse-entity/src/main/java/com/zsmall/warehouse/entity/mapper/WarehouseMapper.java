package com.zsmall.warehouse.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hengjian.common.tenant.mapper.TenantMapperPlus;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.domain.vo.warehouse.WarehouseVo;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 仓库管理Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-22
 */
public interface WarehouseMapper extends TenantMapperPlus<Warehouse, WarehouseVo> {

    @InterceptorIgnore(tenantLine = "true")
    boolean existsWarehouseSystemCode(@Param("warehouseSystemCode") String warehouseSystemCode);

    @InterceptorIgnore(tenantLine = "true")
    boolean isSupportThirdCarrier(@Param("warehouseSystemCode") String warehouseSystemCode);

    /**
     *  根据SKU唯一编号 查询所属的仓库信息
     * @param productSkuCode 商品SKU唯一编号
     * @return
     */
    List<Warehouse> getWarehouseByProductSkuCode(@Param("productSkuCode") String productSkuCode);
    /**
     *  根据仓库系统编码获取仓库地址信息
     * @param warehouseSystemCode 仓库系统编码
     * @return
     */
    String getWarehouseAddressInfo(@Param("warehouseSystemCode") String warehouseSystemCode);

    /**
     * 根据仓库系统编码获取仓库可配送国家信息
     * @param warehouseSystemCode
     * @return
     */
    List<String> listCountryByWarehouseSystemCode(@Param("warehouseSystemCode") String warehouseSystemCode);
}
