package com.zsmall.warehouse.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 物流服务商
 * @TableName shipping_service
 */
@TableName(value ="shipping_service")
@Data
@EqualsAndHashCode(callSuper=false)
public class ShippingService extends NoDeptBaseEntity {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 快递服务编码
     */
    private String code;

    /**
     * 快递服务名称
     */
    private String name;

    /**
     * 物流商唯一代号
     */
    private String logisticsSlug;

    /**
     * 国家二位代号
     */
    private String countryCode;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
