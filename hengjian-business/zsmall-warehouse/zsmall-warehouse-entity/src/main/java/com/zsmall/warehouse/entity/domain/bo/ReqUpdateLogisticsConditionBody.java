package com.zsmall.warehouse.entity.domain.bo;

import com.zsmall.common.domain.dto.LogisticsConditionBody;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 请求参数-修改物流条件
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "请求参数-修改物流条件")
public class ReqUpdateLogisticsConditionBody {

    @Schema(title = "物流条件")
    private LogisticsConditionBody condition;

    @Schema(title = "物流条件id")
    private Long conditionId;

    @Schema(title = "是否员工账号")
    private Boolean isDepot;

    @Schema(title = "物流模板ID")
    private Long logisticsId;

}
