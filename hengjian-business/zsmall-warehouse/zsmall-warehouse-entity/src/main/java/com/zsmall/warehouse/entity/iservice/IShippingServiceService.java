package com.zsmall.warehouse.entity.iservice;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.warehouse.entity.domain.ShippingService;
import com.zsmall.warehouse.entity.domain.bo.shippingService.ShippingServiceBo;
import com.zsmall.warehouse.entity.domain.vo.shippingService.ShippingServiceVo;
import com.zsmall.warehouse.entity.mapper.ShippingServiceMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据层：物流服务商表
 */
@Service
@Slf4j
public class IShippingServiceService extends ServiceImpl<ShippingServiceMapper, ShippingService> {

    /**
     * 查询物流服务商列表
     */
    public TableDataInfo<ShippingServiceVo> queryPageList(ShippingServiceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ShippingService> lqw = buildQueryWrapper(bo);
        lqw.ne(ShippingService::getName, "Others");
        lqw.orderByDesc(ShippingService::getSort);
        Page<ShippingServiceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询物流服务商列表
     */
    public List<ShippingServiceVo> queryList() {
        LambdaQueryWrapper<ShippingService> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ShippingService::getSort);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 名字是否重复
     * @param name
     * @return
     */
    public Boolean existsName(String name) {
        LambdaQueryWrapper<ShippingService> lqw = Wrappers.lambdaQuery();
        lqw.eq(ShippingService::getName, name);
        return baseMapper.exists(lqw);
    }

    public ShippingService getByName(String name) {
        LambdaQueryWrapper<ShippingService> lqw = Wrappers.lambdaQuery();
        lqw.eq(ShippingService::getName, name);
        return baseMapper.selectOne(lqw);
    }

    public ShippingService getByCode(String code) {
        LambdaQueryWrapper<ShippingService> lqw = Wrappers.lambdaQuery();
        lqw.eq(ShippingService::getCode, code);
        return baseMapper.selectOne(lqw);
    }

    private LambdaQueryWrapper<ShippingService> buildQueryWrapper(ShippingServiceBo bo) {
        LambdaQueryWrapper<ShippingService> lqw = Wrappers.lambdaQuery();
        lqw.eq(StrUtil.isNotBlank(bo.getName()), ShippingService::getName, bo.getName());
        return lqw;
    }

}




