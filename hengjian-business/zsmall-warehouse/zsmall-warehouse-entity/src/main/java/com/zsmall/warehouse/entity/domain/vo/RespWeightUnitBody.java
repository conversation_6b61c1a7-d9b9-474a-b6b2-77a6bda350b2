package com.zsmall.warehouse.entity.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/1/20
 **/
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
@Schema(name = "响应信息-重量单位信息")
public class RespWeightUnitBody {

    @Schema(title = "单位（英文）")
    private String unitEn;

    @Schema(title = "单位（中文）")
    private String unitCn;

}
