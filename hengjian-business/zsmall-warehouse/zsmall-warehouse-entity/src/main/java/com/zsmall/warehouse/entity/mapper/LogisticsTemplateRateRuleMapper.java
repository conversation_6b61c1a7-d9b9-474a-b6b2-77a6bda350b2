package com.zsmall.warehouse.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zsmall.warehouse.entity.domain.LogisticsTemplateRateRule;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【logistics_template_rate_rule(物流模板费率规则表)】的数据库操作Mapper
* @createDate 2023-05-06 12:02:03
* @Entity generator.domain.LogisticsTemplateRateRule
*/
public interface LogisticsTemplateRateRuleMapper extends BaseMapper<LogisticsTemplateRateRule> {

    @InterceptorIgnore(tenantLine = "true")
    List<LogisticsTemplateRateRule> queryByProductSkuCode(String productSkuCode);

}




