package com.zsmall.warehouse.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.warehouse.entity.domain.LogisticsWarehouseRelation;
import com.zsmall.warehouse.entity.mapper.LogisticsWarehouseRelationMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据库层：物流模板-仓库关联表
 */
@Service
@Slf4j
public class ILogisticsWarehouseRelationService extends ServiceImpl<LogisticsWarehouseRelationMapper, LogisticsWarehouseRelation> {

    public void removeByTemplateId(Long logisticsTemplateId) {
        log.info("进入【根据物流模板id删除物流模板与仓库关联】方法，logisticsTemplateId = {}", logisticsTemplateId);
        LambdaQueryWrapper<LogisticsWarehouseRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LogisticsWarehouseRelation::getLogisticsTemplateId, logisticsTemplateId);
        this.remove(queryWrapper);
    }

    public List<Long> getByTemplateId(Long logisticsTemplateId) {
        log.info("进入【根据物流模板id查询仓库id】方法，logisticsTemplateId = {}", logisticsTemplateId);
        LambdaQueryWrapper<LogisticsWarehouseRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LogisticsWarehouseRelation::getLogisticsTemplateId, logisticsTemplateId);
        List<LogisticsWarehouseRelation> list = this.list(queryWrapper);
        return list.stream().map(LogisticsWarehouseRelation::getWarehouseId).collect(Collectors.toList());
    }

    /**
     * 查询物流模板关联的仓库名称
     * @param logisticsTemplateId
     * @return
     */
    public List<String> queryWarehouseName(Long logisticsTemplateId) {
        log.info("进入【查询物流模板关联的仓库名称】方法，logisticsTemplateId = {}", logisticsTemplateId);
        return baseMapper.queryWarehouseName(logisticsTemplateId);
    }


    public List<Long> getTemplateIdListByWarehouseId(Long warehouseId) {
        log.info("进入【根据仓库id查询物流模板id】方法，warehouseId = {}", warehouseId);
        LambdaQueryWrapper<LogisticsWarehouseRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LogisticsWarehouseRelation::getWarehouseId, warehouseId);
        List<LogisticsWarehouseRelation> list = this.list(queryWrapper);
        return list.stream().map(LogisticsWarehouseRelation::getLogisticsTemplateId).collect(Collectors.toList());
    }
}




