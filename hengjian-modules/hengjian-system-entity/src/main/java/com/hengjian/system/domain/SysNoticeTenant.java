package com.hengjian.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.hengjian.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * 租户对象 sys_tenant
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_notice_tenant")
public class SysNoticeTenant extends BaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 公告ID
     */
    private Long noticeId;

    /**
     * 已读状态
     */
    private Integer readState;

    /**
     * 已读时间
     */
    private Date readTime;

    /**
     * 已读用户id
     */
    private Long readUserId;

}
