package com.hengjian.system.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.hengjian.system.domain.SysNoticeTenant;

import java.io.Serializable;
import java.util.Date;


/**
 * sys_notice_tenant
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = SysNoticeTenant.class)
public class SysNoticeTenantVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 公告ID
     */
    private Long noticeId;

    /**
     * 已读状态
     */
    private Integer readState;

    /**
     * 已读时间
     */
    private Date readTime;

    /**
     * 已读用户id
     */
    private Long readUserId;


}
