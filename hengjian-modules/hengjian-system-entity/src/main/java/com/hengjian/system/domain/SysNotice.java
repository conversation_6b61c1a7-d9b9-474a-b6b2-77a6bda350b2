package com.hengjian.system.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.hengjian.common.tenant.core.TenantEntity;

import java.util.List;


/**
 * 通知公告表 sys_notice
 *
 * <AUTHOR> Li
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_notice", autoResultMap = true)
public class SysNotice extends TenantEntity {

    /**
     * 公告ID
     */
    @TableId(value = "notice_id")
    private Long noticeId;

    /**
     * 公告标题
     */
    private String noticeTitle;

    /**
     * 公告类型（1通知 2公告）
     */
    private String noticeType;

    /**
     * 公告内容
     */
    private String noticeContent;

    /**
     * 公告状态（0正常 1关闭）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 通知范围（通知的租户类型）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> tenantTypes;

    /**
     * 头像存储桶Id
     */
    @TableField(updateStrategy= FieldStrategy.IGNORED)
    private Long imageOssId;

    /**
     * 头像存储路径
     */
    private String imageSavePath;

    /**
     * 头像展示地址
     */
    private String imageShowUrl;


    @TableField(exist = false)
    private List<SysNoticeTenant> noticeTenants;

}
