package com.hengjian.system.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 用户信息视图对象 sys_user
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SysUserSettleInVo {

    /**
     * 是否完善信息标识
     */
    private String extraPerfectionFlag;

    /**
     * 拒绝理由
     */
    private String reviewReason;
    /**
     * 入驻审核状态
     */
    private String reviewState;

    /**
     * 用户审核状态
     */
    private String userReviewState;

    /**
     * 是否提示审核拒绝
     */
    private Boolean isSupTip = false;

    public SysUserSettleInVo(String extraPerfectionFlag, String reviewReason, String reviewState) {
        this.extraPerfectionFlag = extraPerfectionFlag;
        this.reviewReason = reviewReason;
        this.reviewState = reviewState;
    }
}
