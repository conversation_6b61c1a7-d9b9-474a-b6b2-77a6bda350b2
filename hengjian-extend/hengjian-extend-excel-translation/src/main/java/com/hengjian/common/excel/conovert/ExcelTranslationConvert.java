package com.hengjian.common.excel.conovert;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.excel.annotation.ExcelTranslationFormat;
import com.hengjian.common.translation.core.TranslationInterface;
import com.hengjian.common.translation.core.handler.TranslationHandler;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;

@Slf4j
public class ExcelTranslationConvert implements Converter<Object> {
    @Override
    public Class<?> supportJavaTypeKey() {
//        Console.log("ExcelTranslationConvert.............supportJavaTypeKey....{}", Object.class);
        return Object.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
//        Console.log("ExcelTranslationConvert.............supportExcelTypeKey");
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Object convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
//        Console.log("ExcelTranslationConvert.............convertToJavaData");
        // 目前暂不考虑导入实现
        return Convert.toStr(cellData.getData());
    }

    @Override
    public WriteCellData<?> convertToExcelData(Object object, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        Console.log("ExcelTranslationConvert.............convertToExcelData");
        if (ObjectUtil.isNull(object)) {
            return new WriteCellData<>("");
        }

        // 默认原有值
        String value = Convert.toStr(object);
        ExcelTranslationFormat anno = getAnnotation(contentProperty.getField());
        //判断注解是否有表格翻译注解
        if(anno != null) {
            String type = anno.type();
            // 从翻译工具类中的列表获取翻译实现类
            for(String key: TranslationHandler.TRANSLATION_MAPPER.keySet()) {
                if(StringUtils.equals(type, key)) {
                    TranslationInterface<?> translationInterface = TranslationHandler.TRANSLATION_MAPPER.get(key);

                    Object translation = translationInterface.translation(object, "");
                    value = Convert.toStr(translation);
                    break;
                }
            }
        }

        return new WriteCellData<>(value);
    }

    private ExcelTranslationFormat getAnnotation(Field field) {
        return AnnotationUtil.getAnnotation(field, ExcelTranslationFormat.class);
    }
}
